<template>
  <view class="h-80vh bg-white flex flex-col rd-t-2">
    <!-- 标题栏 -->
    <view class="flex justify-between items-center p-4 pb-2 recommended-questions-title" ref="titleRef">
      <text class="text-lg font-bold">推荐问题</text>
      <view class="text-18 p-16rpx i-ic-baseline-close" @click="$emit('close')"></view>
    </view>

    <!-- 滚动内容区域 -->
    <scroll-view scroll-y class="flex-1 px-4 pt-2 w-full box-border" :style="{ height: scrollViewHeight }">
      <view class="space-y-3">
        <view v-for="(item, index) of questions" :key="index"
              class="p-3 text-primary bg-white rd-2 border border-primary border-solid"
              hover-class="opacity-80"
              @click="handleQuestionSelect(item)">
          <text class="text-base text-#333">{{ item }}</text>
        </view>
      </view>
      <!-- 底部留白空间 -->
      <view class="h-20 w-full"></view>
    </scroll-view>
  </view>
</template>

<script setup>
import { computed, ref, onMounted, nextTick } from 'vue'
import { useApplicationStore } from '@/stores/application'

const props = defineProps({
  // 可以传入自定义问题列表，如果没有则使用appStore中的推荐问题
  customQuestions: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['select', 'close'])

// 获取应用store中的推荐问题
const appStore = useApplicationStore()

// 计算最终显示的问题列表
const questions = computed(() => {
  // 如果有自定义问题，优先使用自定义问题
  if (props.customQuestions && props.customQuestions.length > 0) {
    return props.customQuestions
  }
  // 否则使用appStore中的推荐问题
  return appStore.parameters.suggested_questions || []
})

// 标题元素引用
const titleRef = ref(null);
// 滚动区域高度
const scrollViewHeight = ref('calc(80vh - 60px)');

// 计算滚动区域高度
const calculateScrollViewHeight = () => {
  nextTick(() => {
    const query = uni.createSelectorQuery();
    query.select('.recommended-questions-title').boundingClientRect();

    query.exec((res) => {
      if (!res || !res[0]) {
        console.log('无法获取推荐问题标题的位置信息');
        return;
      }

      const titleRect = res[0]; // 标题的位置信息

      // 计算可用高度 (80vh - 标题高度 - 底部安全区域)
      const availableHeight = uni.getSystemInfoSync().windowHeight * 0.8 - titleRect.height - 20;
      console.log('推荐问题弹窗可用高度:', availableHeight);

      // 设置高度
      scrollViewHeight.value = `${availableHeight}px`;
    });
  });
};

// 组件挂载后计算高度
onMounted(() => {
  calculateScrollViewHeight();
});

// 处理问题选择
const handleQuestionSelect = (question) => {
  emit('select', question)
  // 选择后自动关闭弹出层
  emit('close')
}
</script>

<style scoped>
/* 可以添加自定义样式 */
</style>
