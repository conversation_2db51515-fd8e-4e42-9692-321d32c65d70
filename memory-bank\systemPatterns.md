[2025-04-07 12:03:00] - 商户选择组件设计规范
1. 参数模式：
   - 支持value传入商户ID(Number/String)或完整对象(Object)
   - 内部通过计算属性统一处理
2. 事件规范：
   - 使用@select事件传递完整商户对象
   - 保持v-model双向绑定
3. 兼容性要求：
   - 确保与useMerchantSelect兼容
   - 支持690+商户数据场景
[2025-03-31 11:40:08] - 消息记录存储模式变更：
- 前端不再直接保存会话历史
- 改为通过后端接口获取完整消息记录
- 移除本地saveCurrentSession调用
[2025-03-29 02:11:17] - 更新请求授权模式：
- 采用动态Authorization头机制
- 仅在用户登录状态下添加Bearer Token
- 统一了401/403错误处理流程
[2025-03-31 15:48:00] - 完善API响应状态码处理模式：
- 采用HTTP状态码和业务状态码双重检查机制
  - HTTP状态码处理范围：<200或>=300
  - 业务状态码检查：res.data.status !== 200
- 错误处理统一流程：
  - 记录错误日志
  - 抛出包含错误信息的异常
  - 保持与前端错误处理的一致性
# 系统模式文档

## 商户信息结构
[2025-03-29 00:52:30] - 记录商户信息API返回结构
- 字段说明:
  - `label`: 商户名称(字符串)
  - `merchant_id`: 商户唯一标识(ID)
- 使用规范:
  - 前端统一使用`label`显示商户名称
  - 业务逻辑中使用`merchant_id`作为商户标识
  - 兼容处理: 同时支持`merchantName`和`id`的旧字段

## 商户选择器实现
[2025-03-29 01:08:08] - 重构商户选择器实现
- 使用computed属性格式化商户列表
- 格式化为{text, value}结构供选择器使用
- 新增computed getter/setter自动同步商户状态
- 简化事件处理逻辑
- 优先使用v-model绑定数据

## 组件导入模式
[2025-04-01 16:40:00] - 重构组件导入方式
- 采用easycom自动导入机制
- 移除显式import语句
- 组件命名规范：
  - 文件名必须与组件名一致
  - 使用PascalCase命名
  - 放置在components目录下
- 优势：
  - 减少样板代码
  - 自动按需导入
  - 保持组件标签不变
- 注意事项：
  - 确保组件文件名与标签名匹配
  - 需要配置unplugin-vue-components