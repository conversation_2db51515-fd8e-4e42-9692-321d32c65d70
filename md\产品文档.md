以下是整合所有需求后的完整产品文档，请基于当前项目的信息，完成如下全部功能：

## 预处理
项目基本底座已完成，uniapp cli模式，unocss，unocss-preset-weapp
请清晰阅读 D:\code\project\ai-chat路径中项目文件，再执行代码生成和处理。

## AI 问答小程序 - 前端产品文档

**1. 产品概述**

* **产品名称:**  AI 智能问答助手 (暂定名，可根据实际情况调整)
* **产品定位:**  一款基于大模型的、界面简洁、交互便捷的 AI 问答小程序，旨在为用户提供高效、智能的问答服务。
* **目标用户:**  对 AI 问答、信息查询、知识获取有需求的用户，例如学生、职场人士、信息爱好者等。
* **核心价值:**
    * **智能问答:**  利用大模型提供准确、深入的答案，解决用户疑问。
    * **简洁易用:**  UI 简洁，操作流畅，无需复杂学习即可快速上手。
    * **多模态输入:**  支持文字、图片、语音、文件等多种输入方式，满足不同场景下的用户需求。
    * **个性化体验:**  持续优化交互和功能，提供更贴合用户需求的智能问答服务。

**2. 功能列表**

* **核心功能**
    * **AI 问答:**
        * 用户通过文字、图片、语音、文件等方式向 AI 提问。
        * AI 模型返回答案，使用 towxml 渲染。
        * 支持连续对话，保持上下文理解。
        * 支持开启新会话，清空当前对话内容。
    * **账号系统:**
        * 账号密码登录/注册。
        * 微信手机号快速登录。
        * 用户信息管理（头像、昵称等，可选）。
* **辅助功能 (初期可考虑精简，后续迭代)**
    * **历史记录:**  查看用户的提问历史和 AI 回答。
    * **收藏/点赞:**  收藏重要的问答，点赞高质量回答（可选）。
    * **分享:**  将问答内容分享给好友或社交平台（可选）。
    * **设置:**
        * 主题切换（浅色/深色模式，可选）。
        * 清除缓存。
        * 关于我们。

**3. 技术方案**

**3.1 前端技术栈**

* **框架:**  UniApp (选择 Vue3 版本)
* **UI 框架:**  无第三方 UI 组件库，完全基于 UniApp 基础组件和 UnoCSS 构建。
* **样式方案:**  UnoCSS + `unocss-preset-weapp`
    * 所有样式均通过 UnoCSS class 原子化 CSS 来实现，无需自定义样式文件。
    * 使用 `unocss-preset-weapp` 适配小程序环境，解决单位转换等问题。
    * 利用 UnoCSS 的 Iconify 功能，使用图标库，例如 `ic` 前缀。
* **图标库:**  Iconify (通过 UnoCSS 引入，例如 `i-ic-[图标集名称]-[图标名称]`)
* **数据存储:**  Pinia (Vuex 的替代方案，更轻量、易用)
* **组件模式:**  Easycom (自动按需引入组件，简化组件使用)
* **富文本渲染:**  towxml (用于渲染大模型返回的 Markdown 或 HTML 数据)
* **登录方案:**
    * 默认账号密码登录 (使用 UniApp 的表单组件和 API 实现)
    * 微信手机号快速登录 (使用 `uni.login` 和 `uni.getUserProfile` 等 API，以及按钮组件的 `open-type="getPhoneNumber"` 属性)
* **按钮实现:**  除特殊情况 (微信获取手机号按钮) 外，所有按钮均使用 `<view>` 组件 + UnoCSS 样式模拟。
* **主色调:** `#38C948` (绿色)

**3.2  UnoCSS 样式方案细节**

* **原子化 CSS:**  充分利用 UnoCSS 的原子化能力，例如 `text-lg`, `bg-gray-100`, `p-4`, `rounded-md` 等 class。
* **主题色:**  定义 UnoCSS 的主题配置，将 `#38C948` 设置为主色调，方便在样式中引用，例如 `bg-primary`, `text-primary`, `border-primary`。
* **响应式设计:**  利用 UnoCSS 的响应式断点，例如 `sm:`, `md:`, `lg:` 前缀，适配不同屏幕尺寸 (小程序屏幕尺寸相对统一，初期可简化响应式设计)。
* **Iconify 图标:**  使用 `i-ic-[图标集名称]-[图标名称]` 的格式引入图标，例如 `i-ic-carbon-chat-bot` (示例，具体图标根据实际需求选择)。
* **自定义配置:**  根据项目需求，自定义 UnoCSS 配置，例如添加自定义颜色、字体、断点等。

**3.3  Towxml 渲染方案**

* **数据来源:**  后端大模型返回的文本数据，格式可以是 Markdown 或 HTML。
* **组件使用:**  引入 towxml 组件，将大模型返回的数据传递给 towxml 组件进行渲染。
* **样式定制:**  towxml 默认样式可能需要根据小程序整体 UI 风格进行调整，可以通过 towxml 提供的 API 或 CSS 覆盖进行定制 (尽量使用 UnoCSS class 来覆盖)。
* **图片处理:**  towxml 对图片有一定处理能力，需要考虑图片加载优化和错误处理。

**3.4  Pinia 状态管理**

* **全局状态:**  用于管理用户登录状态、用户信息、全局配置等。
* **模块化:**  根据功能模块划分 Pinia Store，例如用户 Store, 对话 Store, 设置 Store 等，方便维护和管理。
* **数据持久化:**  考虑使用 Pinia 插件实现数据持久化，例如用户登录状态、设置信息等，提升用户体验。

**3.5  Easycom 组件模式**

* **组件目录:**  统一将自定义组件放置在 `components` 目录下。
* **自动引入:**  利用 Easycom 的自动引入特性，在页面或组件中直接使用组件名，无需手动 import 和注册。
* **组件命名:**  组件命名采用 PascalCase 命名法 (例如 `ChatMessage.vue`)。

**4.  UI/UX 设计**

**4.1  设计原则**

* **简洁至上:**  界面元素精简，突出核心功能，避免冗余信息和复杂操作。
* **清晰易懂:**  信息层级清晰，导航明确，用户能快速找到所需功能。
* **操作便捷:**  交互流程流畅自然，减少用户操作步骤，提升使用效率。
* **视觉统一:**  整体视觉风格统一，色彩搭配协调，符合主色调 `#38C948`。
* **用户友好:**  关注用户使用习惯，提供友好的操作提示和反馈。

**4.2  页面结构 (示例)**

* **首页 (对话页):**
    * **顶部:**  应用标题 (例如 "智能问答助手")，新会话按钮，设置入口图标 (Iconify 图标)。
    * **中间:**  对话消息展示区域 (使用 `scroll-view` 实现滚动)，用户消息和 AI 消息区分显示，使用气泡样式。
    * **底部:**  输入区域，包括：
        * 文本输入框 (`input`)，支持多行输入。
        * 输入方式切换按钮 (Iconify 图标，例如 语音、图片、文件)。
        * 发送按钮 (Iconify 图标，例如 发送箭头)。
* **登录/注册页:**
    * **Logo:**  应用 Logo (可选)。
    * **账号密码登录:**  账号输入框、密码输入框、登录按钮、注册入口。
    * **微信手机号登录:**  微信登录按钮 (特殊按钮，使用 `button` 组件 `open-type="getPhoneNumber"`)。
* **历史记录页 (可选):**
    * **顶部:**  页面标题 "历史记录"，返回按钮 (Iconify 图标)。
    * **中间:**  历史问答列表，按时间倒序排列，展示部分问题内容和时间。
    * **底部:**  (可选) 加载更多按钮。
* **设置页 (可选):**
    * **顶部:**  页面标题 "设置"，返回按钮 (Iconify 图标)。
    * **列表:**  设置项列表，例如 "主题切换"、"清除缓存"、"关于我们" 等。

**4.3  交互设计要点**

* **对话交互:**
    * 用户输入后，AI 回答应及时显示，并有加载状态提示 (例如 loading 图标)。
    * 对话消息气泡区分用户和 AI，颜色、边框等样式区分。
    * 消息列表自动滚动到底部，保持最新消息可见。
    * 支持长按消息复制、删除等操作 (可选)。
    * 点击新会话按钮时，弹出确认对话框，确认后清空当前对话内容。
* **输入方式切换:**
    * 点击输入方式切换按钮，弹出或展开输入方式选择面板 (例如 语音、图片、文件 图标)。
    * 选择不同输入方式后，切换到对应的输入界面。
* **按钮反馈:**
    * 所有可点击元素 (按钮、图标) 均应有点击反馈效果 (例如 颜色变化、涟漪效果，UnoCSS 可以通过 `:active:` 等伪类实现)。
* **加载状态:**
    * 在数据加载、AI 模型响应时，显示合适的加载状态提示 (例如 loading 动画、文字提示)。
* **错误处理:**
    * 对网络错误、服务异常等情况进行友好提示。
* **空状态:**
    * 在没有对话历史、没有搜索结果等情况下，显示友好的空状态提示。

**4.4  视觉风格参考**

* **整体风格:**  参照 Open AI 和 DeepSeek 等产品的简洁、现代、科技感风格。
* **配色:**  以主色调 `#38C948` 为主，搭配浅灰色、白色等，营造清新、专业的视觉感受。
* **字体:**  选择清晰易读的字体，例如 微软雅黑、PingFang SC 等。
* **图标:**  使用 Iconify 图标库，选择线条简洁、风格统一的图标，与整体 UI 风格保持一致。
* **圆角:**  适当运用圆角 (例如 UnoCSS 的 `rounded-md`, `rounded-lg`)，增加界面的柔和感。
* **阴影:**  谨慎使用阴影，避免过度装饰，保持界面简洁 (例如 UnoCSS 的 `shadow-sm`, `shadow-md`)。

**5.  开发计划 (简要)**

* **第一阶段 (MVP - Minimum Viable Product):**
    * 完成账号密码登录/注册功能。
    * 实现文字输入和 AI 问答核心功能。
    * 完成基本的对话界面 UI。
    * 使用 towxml 渲染 AI 返回结果。
    * 实现开启新会话功能。
* **第二阶段 (功能完善):**
    * 接入微信手机号快速登录。
    * 实现图片、语音、文件等输入方式。
    * 添加历史记录功能。
    * 优化 UI 交互，提升用户体验。

**总结**

本产品文档旨在为 AI 问答小程序的前端开发提供清晰的指导，涵盖了产品定位、功能、技术方案、UI/UX 设计等方面。  在实际开发过程中，可以根据市场反馈和用户需求进行灵活调整和迭代，不断完善产品功能和用户体验。

@Web 