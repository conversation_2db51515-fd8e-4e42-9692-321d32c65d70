<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'


import { changePassword } from '@/services/api'

// 用户Store
const userStore = useUserStore()
const isLoggedIn = computed(() => userStore.checkIsLoggedIn())

// 如果用户未登录，跳转到登录页面
if (!isLoggedIn.value) {
  uni.redirectTo({
    url: '/pages/login/index'
  })
}

// 表单数据
const formData = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 错误信息
const errors = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: '',
  form: ''
})

// 加载状态
const isLoading = ref(false)

// 表单验证
const validateForm = () => {
  // 重置错误
  errors.value = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
    form: ''
  }

  let isValid = true

  // 验证旧密码
  if (!formData.value.oldPassword) {
    errors.value.oldPassword = '请输入旧密码'
    isValid = false
  }

  // 验证新密码
  if (!formData.value.newPassword) {
    errors.value.newPassword = '请输入新密码'
    isValid = false
  } else if (formData.value.newPassword.length < 6) {
    errors.value.newPassword = '新密码不能少于6个字符'
    isValid = false
  }

  // 验证确认密码
  if (!formData.value.confirmPassword) {
    errors.value.confirmPassword = '请确认新密码'
    isValid = false
  } else if (formData.value.confirmPassword !== formData.value.newPassword) {
    errors.value.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }

  return isValid
}

// 处理修改密码
const handleChangePassword = async () => {
  // 表单验证
  if (!validateForm()) {
    return
  }

  // 设置加载状态
  isLoading.value = true

  try {
    // 调用修改密码API
    await changePassword(formData.value.oldPassword, formData.value.newPassword)

    // 修改成功提示
    uni.showToast({
      title: '密码修改成功',
      icon: 'success'
    })

    // 跳转回用户中心
    setTimeout(() => {
      uni.navigateBack()
    }, 1500)
  } catch (error) {
    console.error('修改密码失败:', error)

    // 处理错误
    if (error.message && error.message.includes('old_password')) {
      errors.value.oldPassword = '旧密码不正确'
    } else {
      errors.value.form = error.message || '修改密码失败，请稍后再试'
    }
  } finally {
    isLoading.value = false
  }
}

// 返回上一页
const handleGoBack = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="change-password-container">
    <view class="form-container">
      <view class="form-title">修改密码</view>

      <!-- 错误提示 -->
      <view v-if="errors.form" class="form-error">
        {{ errors.form }}
      </view>

      <!-- 旧密码 -->
      <view class="form-group">
        <label class="form-label">旧密码</label>
        <BaseInput
          v-model="formData.oldPassword"
          type="password"
          placeholder="请输入旧密码"
          :error="errors.oldPassword"
        />
      </view>

      <!-- 新密码 -->
      <view class="form-group">
        <label class="form-label">新密码</label>
        <BaseInput
          v-model="formData.newPassword"
          type="password"
          placeholder="请输入新密码"
          :error="errors.newPassword"
        />
      </view>

      <!-- 确认密码 -->
      <view class="form-group">
        <label class="form-label">确认密码</label>
        <BaseInput
          v-model="formData.confirmPassword"
          type="password"
          placeholder="请再次输入新密码"
          :error="errors.confirmPassword"
        />
      </view>

      <!-- 提交按钮 -->
      <BaseButton
        @click="handleChangePassword"
        class="submit-button"
        :loading="isLoading"
      >
        确认修改
      </BaseButton>

      <!-- 取消按钮 -->
      <BaseButton
        @click="handleGoBack"
        class="cancel-button"
        type="default"
      >
        返回
      </BaseButton>
    </view>
  </view>
</template>

<style>
.change-password-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20px;
}

.form-container {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.form-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
  text-align: center;
}

.form-error {
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  padding: 10px;
  border-radius: 4px;
  color: #ff4d4f;
  margin-bottom: 15px;
  font-size: 14px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
}

.submit-button {
  width: 100%;
  margin-top: 10px;
  margin-bottom: 10px;
}

.cancel-button {
  width: 100%;
  background-color: #f0f0f0;
  color: #666;
  border-color: #d9d9d9;
}
</style>