<script>
import { useUserStore } from './stores/user'
import { useChatStore } from './stores/chat'
import { useHistoryStore } from './stores/history'
import { useApplicationStore } from './stores/application'


export default {
  onLaunch: async function () {
    console.log('App Launch')
    // 初始化用户状态
    const userStore = useUserStore()
    await userStore.initUserState()

    // 获取微信code
    const loginRes = await new Promise((resolve) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: resolve
      })
    })


    // 如果未登录、有微信code且用户未手动退出，尝试静默登录
    if (loginRes?.code && !userStore.manuallyLoggedOut) {
      try {
        await userStore.wxMiniLogin(loginRes.code)
      } catch (error) {
        console.error('微信静默登录失败:', error);
      }
    }



    // 初始化应用状态
    const appStore = useApplicationStore()
    appStore.fetchParameters()

    // 加载会话列表
    const historyStore = useHistoryStore()
    historyStore.loadConversations()
  },
  onShow: function () {
    console.log('App Show')
  },
  onHide: function () {
    console.log('App Hide')

    // 保存聊天历史
    const chatStore = useChatStore()
    chatStore.saveHistoryMessages()
  },
}
</script>

<style>
/*每个页面公共css */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  height: 100vh;
}

/* 确保scroll-view滚动正常 */
::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}

.h2w__main{
  padding:16rpx 32rpx !important;
  margin:0 !important;
  border-radius: 16rpx;
}

:root,
page {
  --wot-color-warning: #FFA700;
  --wot-color-theme: #38C948;
}
</style>