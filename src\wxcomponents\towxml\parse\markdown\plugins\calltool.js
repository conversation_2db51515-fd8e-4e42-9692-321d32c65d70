module.exports = md => {
    const temp = md.renderer.rules.fence.bind(md.renderer.rules)
    md.renderer.rules.fence = (tokens, idx, options, env, slf) => {
        const token = tokens[idx]
        const code = token.content.trim();
        if (token.info.startsWith('calltool') ) {
            // 检查是否有错误信息
            const lines = code.split('\n');
            let toolName = code;
            let errorMsg = '';

            // 如果有多行，第一行是工具名称，其余行可能是错误信息
            if (lines.length > 1) {
                toolName = lines[0];
                // 如果是错误状态，第二行开始可能是错误信息
                if (token.info.includes('error') && lines.length > 1) {
                    errorMsg = lines.slice(1).join(' ').trim();
                }
            }

            // 如果有错误信息，添加到data-error属性，并转义HTML特殊字符
            let escapedErrorMsg = '';
            if (errorMsg) {
                escapedErrorMsg = errorMsg
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;');
            }
            const errorAttr = errorMsg ? ` data-error="${escapedErrorMsg}"` : '';

            // 提取工具ID（如果存在）
            let toolId = '';
            // 使用正则表达式从token.info中提取ID
            // 例如从 "calltool.start.a1b2" 中提取 "a1b2"
            const idMatch = token.info.match(/calltool\.(start|stop|error|complete)\.([a-zA-Z0-9]+)/);
            if (idMatch && idMatch[2]) {
                toolId = idMatch[2];
            }

            // 添加data-id属性
            const idAttr = toolId ? ` data-id="${toolId}"` : '';

            // 提取状态（start, stop, error, complete）
            let status = 'start';
            if (token.info.includes('complete')) {
                status = 'complete';
            } else if (token.info.includes('stop')) {
                status = 'stop';
            } else if (token.info.includes('error')) {
                status = 'error';
            }

            // 使用基本的calltool类，并添加状态类
            return `<view class="calltool calltool-${status}"${errorAttr}${idAttr}><view class="icon"></view>${toolName}</view>`;
        };
        return temp(tokens, idx, options, env, slf)
    }
};