# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-03-29 00:06:17 - 更新项目目标和关键功能

## Project Goal
* 开发商户管理系统
* 实现多商户切换功能
* 优化用户登录流程

## Key Features
* 商户选择与切换功能
* 统一的用户参数处理
* 优化的登录状态管理
* 商户数据缓存机制

## Overall Architecture
* 基于Vue.js的前端架构
* 使用状态管理(store)处理商户信息
* 通过API服务层与后端交互