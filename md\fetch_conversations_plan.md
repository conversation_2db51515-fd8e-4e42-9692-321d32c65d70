# 功能策划：点击历史记录回显会话

## 1. 目标

当用户在历史记录列表（例如侧边栏）中点击某个会话项时，能够调用 API 获取该会话的所有消息记录，并在主聊天界面中显示这些记录。

## 2. API 信息

-   **Endpoint:** `GET http://192.168.85.184:3001/v1/messages`
-   **Query Parameters:**
    -   `user`: 用户标识符 (必填)
    -   `conversation_id`: 会话 ID (必填)
-   **Headers:**
    -   `Authorization: Bearer {api_key}`
-   **Success Response (Example):**
    ```json
    {
      "limit": 20,
      "has_more": false,
      "data": [
        {
          "id": "...",
          "conversation_id": "...",
          "inputs": { ... },
          "query": "User's question",
          "answer": "AI's answer",
          "message_files": [],
          "feedback": null,
          "retriever_resources": [ ... ],
          "created_at": 1705569239
        },
        // ... more messages
      ]
    }
    ```

## 3. 开发步骤

### 3.1. UI 交互 (历史记录列表组件)

-   定位显示历史会话列表的组件（可能在 `src/components/` 或 `src/pages/` 下）。
-   为每个历史会话项（例如 `<div>` 或 `<li>`）绑定一个点击事件监听器 (`@click`)。
-   点击事件触发时，调用一个方法（例如 `handleHistoryItemClick`），并将该项对应的 `conversation_id` 作为参数传递。

### 3.2. 状态管理 (Pinia Store - 建议 `src/stores/chat.js` 或新建)

-   **State:**
    -   `currentConversationId: string | null`：存储当前选中的会话 ID。
    -   `messages: Array<Message>`：存储当前会话的消息列表。定义 `Message` 类型，至少包含 `id`, `query`, `answer`, `role` (用于区分用户/AI)。
    -   `isLoadingMessages: boolean`：标记是否正在加载消息。
    -   `messageError: string | null`：存储加载消息时发生的错误信息。
-   **Actions:**
    -   `fetchMessagesByConversationId(conversationId: string)`:
        1.  设置 `isLoadingMessages` 为 `true`，清空 `messageError`。
        2.  更新 `currentConversationId` 为传入的 `conversationId`。
        3.  从用户状态管理（例如 `userStore`）获取 `userId` 和 `apiKey`。
        4.  调用 API 服务函数 (`getMessages`) 获取消息。
        5.  **成功时:**
            -   处理 API 返回的 `data`，将其转换为 `messages` 状态所需的格式（可能需要添加 `role` 字段）。
            -   更新 `messages` 状态。
            -   设置 `isLoadingMessages` 为 `false`。
        6.  **失败时:**
            -   捕获错误。
            -   设置 `messageError` 为错误信息。
            -   设置 `isLoadingMessages` 为 `false`。
            -   可能需要清空 `messages` 或保留旧数据。
    -   `clearCurrentConversation()`: (可选) 用于清除当前会话状态，例如开始新会话时。

### 3.3. API 服务层 (`src/services/api.js` 或类似文件)

-   创建或更新一个函数 `getMessages(userId: string, conversationId: string, apiKey: string)`:
    -   构造完整的 API URL 和请求头。
    -   使用 `uni.request` 或其他 HTTP 客户端库发起 `GET` 请求。
    -   处理请求参数和 `Authorization` 头。
    -   返回 Promise，resolve 时返回 API 的 `data` 部分，reject 时返回错误信息。

### 3.4. UI 渲染 (主聊天界面 - `src/pages/index/index.vue`?)

-   在组件的 `script setup` 中，引入并使用相关的 Pinia store (`chatStore`)。
-   使用 `computed` 或直接在模板中访问 `chatStore.messages`, `chatStore.isLoadingMessages`, `chatStore.messageError`。
-   **消息显示:**
    -   使用 `v-for` 遍历 `chatStore.messages` 数组。
    -   根据消息的 `role` (或通过判断 `query` 和 `answer` 是否存在) 来决定渲染用户气泡还是 AI 气泡。
    -   显示 `query` 和 `answer` 的内容。
-   **加载状态:**
    -   当 `chatStore.isLoadingMessages` 为 `true` 时，显示加载指示器（例如骨架屏或 loading 图标）。
-   **错误状态:**
    -   当 `chatStore.messageError` 有值时，显示错误提示信息。

### 3.5. 用户标识和 API Key

-   确保在调用 `fetchMessagesByConversationId` action 之前，用户的 `userId` 和 `apiKey` 是可用的（通常在登录后从 `userStore` 获取）。

## 4. 注意事项

-   **数据格式转换:** API 返回的数据结构可能需要调整以适应前端 `messages` 状态的格式。特别是需要明确区分用户消息和 AI 消息。
-   **滚动行为:** 加载历史消息后，聊天界面可能需要滚动到底部或特定位置。
-   **分页:** API 响应中有 `limit` 和 `has_more`，暗示可能需要处理分页加载更多历史消息，但这超出了当前需求范围，可作为后续优化。
-   **性能:** 如果会话消息非常多，一次性加载和渲染可能影响性能，考虑虚拟滚动或分批加载。

## 5. 下一步

-   确认历史记录列表组件的具体位置。
-   确认状态管理的具体 store 文件 (`chat.js` 或 `history.js`)。
-   开始实现上述步骤。