/**
 * 解析消息内容，检查是否存在action_input属性，如果存在则替换JSON部分
 * @param {string} content - 消息内容
 * @returns {string} - 处理后的消息内容
 */
export function parseMessageContent(content) {
  // 如果内容为空或不是字符串，直接返回原内容
  if (!content || typeof content !== 'string') {
    return content;
  }

  // 提取markdown代码块内容，移除markdown的代码格式
  // 处理可能存在的多个markdown代码块
  // 匹配任何类型的代码块，包括所有语言标识和没有语言标识的
  const markdownRegex = /```markdown([^`]+)```/g;
  let processedContent = content;

  // 查找所有markdown代码块并替换
  const matches = [...processedContent.matchAll(markdownRegex)];
  for (const match of matches) {
    if (match && match[1]) {
      const markdownBlock = match[0]; // 完整的markdown代码块，包括```
      const markdownContent = match[1].trim(); // 代码块内容，不包括```
      processedContent = processedContent.replace(markdownBlock, markdownContent);
    }
  }

  // 更新content为处理后的内容，继续执行后续流程
  content = processedContent;

  try {
    // 先尝试使用正则表达式匹配JSON对象及其位置
    const jsonRegex = /(\{\s*"action_name"\s*:\s*"[^"]*"\s*,\s*"action_input"\s*:\s*"([^"]*)"[^\}]*\})/;
    const match = content.match(jsonRegex);

    if (match && match[1] && match[2]) {
      console.log("🚀 ~ parseMessageContent ~ 检测到action_input属性，只替换JSON部分");
      // 处理转义字符
      const actionInput = match[2].replace(/\\n/g, '\n').replace(/\\r/g, '\r').replace(/\\"/g, '"').replace(/\\\\/g, '\\');
      // 只替换JSON部分，保留其他内容
      return content.replace(match[1], actionInput);
    }

    // 如果简单正则表达式匹配失败，尝试更复杂的匹配
    // 匹配完整的JSON对象
    const fullJsonRegex = /(\{[\s\S]*?\})/g;
    let result = content;
    let jsonFound = false;

    // 遍历所有匹配到的JSON对象
    const matches = content.match(fullJsonRegex);
    if (matches) {
      for (const jsonStr of matches) {
        try {
          const jsonObj = JSON.parse(jsonStr);
          // 如果存在action_input属性，则替换该JSON对象
          if (jsonObj.action_input !== undefined) {
            console.log("🚀 ~ parseMessageContent ~ 检测到action_input属性，替换JSON对象");
            result = result.replace(jsonStr, jsonObj.action_input);
            jsonFound = true;
          }
        } catch (jsonError) {
          // 如果解析失败，尝试下一个JSON对象
          continue;
        }
      }
    }

    // 如果找到并处理了JSON对象，返回处理后的结果
    if (jsonFound) {
      return result;
    }
  } catch (error) {
    console.error("解析JSON失败:", error);
    // 如果解析失败，保留原始消息内容
  }

  // 如果没有找到action_input属性或解析失败，返回原始内容
  return content;
}

/**
 * 解析agent_thoughts数组，提取所有thought内容并按顺序拼接
 * 同时检查每个thought中是否包含action_input属性
 * @param {Array} agentThoughts - agent_thoughts数组
 * @returns {string} - 处理后的消息内容
 */
export function parseAgentThoughts(agentThoughts) {
  if (!agentThoughts || !Array.isArray(agentThoughts) || agentThoughts.length === 0) {
    return '';
  }

  try {
    // 按照position属性排序所有thoughts
    const sortedThoughts = [...agentThoughts].sort((a, b) => a.position - b.position);

    // 处理所有thought内容，检查是否包含action_input
    const processedThoughts = sortedThoughts
      .filter(t => t.thought && typeof t.thought === 'string' && t.thought.trim())
      .map(t => {
        // 对每个thought内容进行处理，检查是否包含action_input
        const thought = typeof t.thought === 'string' ? t.thought.trim() : String(t.thought);
        const processed = parseMessageContent(thought);
        return processed;
      });

    // 将所有处理后的thought内容拼接成一个字符串
    if (processedThoughts.length > 0) {
      return processedThoughts.join('\n\n');
    }
  } catch (error) {
    console.error("处理agent_thoughts失败:", error);
  }

  return '';
}
