<template>
  <uni-popup :safe-area="false" ref="popup" type="bottom" @change="onPopupChange">
    <view class="bg-white rounded-t-16px p-32rpx max-h-70vh">
      <view class="flex justify-between items-center mb-24rpx">
        <text class="text-32 font-bold">选择商户</text>
        <view class="text-18 p-16rpx i-ic-baseline-close" @click="close">
        </view>
      </view>

      <scroll-view scroll-y class="max-h-[calc(70vh-120rpx)]">
        <view v-for="merchant in merchants" :key="merchant.merchant_id"
          class="py-24rpx border-b border-gray-200 flex justify-between items-center active:bg-gray-100"
          @click="selectMerchant(merchant)">
          <text>{{ merchant.label || merchant.name }}</text>
          <view v-if="merchant.merchant_id === currentMerchant?.merchant_id" class="text-primary text-34 i-ic-baseline-done"></view>
        </view>
      </scroll-view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, watch, computed } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const merchants = computed(() => userStore.merchantList)

const props = defineProps({
  value: {
    type: [Object, String, Number],
    default: null
  },
  show: {
    type: Boolean,
    default: false
  }
})

// 获取当前选中商户
const currentMerchant = computed(() => {
  if (!props.value) return null
  if (typeof props.value === 'object') return props.value
  return merchants.value.find(m => m.merchant_id === props.value)
})

const emit = defineEmits(['update:value', 'update:show'])

const popup = ref(null)

const selectMerchant = (merchant) => {
  emit('update:value', merchant)
  // 同时触发自定义事件传递完整商户对象
  emit('select', merchant)
  close()
}

const close = () => {
  emit('update:show', false)
}

const onPopupChange = (e) => {
  if (!e.show) {
    close()
  }
}

watch(() => props.show, (newVal) => {
  console.log("🚀 ~ watch ~ props.show:", props.show)
  if (newVal) {
    popup.value?.open()
  } else {
    popup.value?.close()
  }
})
</script>
