/**
 * 一个简单的存储对象，用于通过 uni-app 的本地缓存 API 存储和获取数据。
 * @typedef {Object} Storage
 * @property {Function} getItem - 获取指定键名的数据的方法。
 * @property {Function} setItem - 存储指定键名和值的方法。
 * @property {Function} removeItem - 移除指定键名的数据的方法。
 */

/**
 * 一个名为 storage 的常量，是一个具有 getItem、setItem 和 removeItem 方法的存储对象。
 * @type {Storage}
 */
export default {
  getItem: uni.getStorageSync,
  setItem: uni.setStorageSync,
  removeItem: uni.removeStorageSync
};
