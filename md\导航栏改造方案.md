# 小程序导航栏改造方案

## 背景
替换默认导航头，使用自定义组件`nav-bar.vue`，整合历史记录和新增会话功能。

## 方案设计

```mermaid
graph TD
    A[新导航栏布局] --> B[左侧区域]
    B --> B1[返回按钮]
    B --> B2[操作按钮组]
    A --> C[中间区域]
    C --> C1[标题/商户选择器]
    A --> D[右侧区域]
    D --> D1[胶囊按钮兼容区域]
```

## 实施步骤

### 1. nav-bar.vue组件改造
- 添加左侧操作按钮组插槽
- 保留原有返回按钮功能
- 新增props:
  - `showActions`: 是否显示操作按钮
  - `actionIcons`: 按钮图标配置数组
- 添加事件发射:
  - `action-click`: 按钮点击事件

### 2. index.vue修改
- 移除原有导航栏代码(1377-1410行)
- 集成新的nav-bar组件:
```vue
<nav-bar 
  :show-actions="true"
  :action-icons="actionIcons"
  @action-click="handleActionClick"
>
  <template #center>
    <uni-data-select v-if="formattedMerchants.length" ... />
  </template>
</nav-bar>
```

### 3. 功能迁移
- 历史记录按钮: 绑定到`openHistoryDrawer`方法
- 新增会话按钮: 绑定到`startNewConversation`方法
- 登录状态提示: 通过props动态控制

## 注意事项
1. 按钮必须放在左侧区域（微信小程序限制）
2. 保持与胶囊按钮的安全间距
3. 确保各状态样式一致性

## 测试用例
1. 登录/未登录状态显示
2. 按钮点击功能
3. 商户选择器功能
4. 不同屏幕尺寸适配