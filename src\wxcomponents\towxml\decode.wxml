<block wx:for="{{nodes.children}}" wx:for-index="i" wx:for-item="item" wx:key="i"><block wx:if="{{item.tag===undefined}}">{{item.text}}</block><block wx:if="{{item.tag==='view'}}"><block wx:if="{{item.rely}}"><view data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></view></block><block wx:else><view class="h2w__viewParent"><view data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></view></view></block></block><block wx:if="{{item.tag==='video'}}"><view class="h2w__videoParent"><video data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap" poster="{{item.attrs.poster}}" src="{{item.attrs.src}}"><decode wx:if="{{item.children}}" nodes="{{item}}"/></video></view></block><block wx:if="{{item.tag==='text'}}"><view class="h2w__textParent"><text data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></text></view></block><block wx:if="{{item.tag==='image'}}"><view class="h2w__imageParent"><image data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap" src="{{item.attrs.src}}" mode="{{item.attrs.mode ? item.attrs.mode : 'widthFix'}}" lazy-load="{{item.attr['lazy-load']}}"><decode wx:if="{{item.children}}" nodes="{{item}}"/></image></view></block><block wx:if="{{item.tag==='navigator'}}"><view class="h2w__navigatorParent"><navigator data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap" url="{{item.attrs.href}}"><decode wx:if="{{item.children}}" nodes="{{item}}"/></navigator></view></block><block wx:if="{{item.tag==='swiper'}}"><view class="h2w__swiperParent"><swiper data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></swiper></view></block><block wx:if="{{item.tag==='swiper-item'}}"><view class="h2w__swiper-itemParent"><swiper-item data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></swiper-item></view></block><block wx:if="{{item.tag==='block'}}"><view class="h2w__blockParent"><block data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></block></view></block><block wx:if="{{item.tag==='form'}}"><view class="h2w__formParent"><form data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></form></view></block><block wx:if="{{item.tag==='input'}}"><view class="h2w__inputParent"><input data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></input></view></block><block wx:if="{{item.tag==='textarea'}}"><view class="h2w__textareaParent"><textarea data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></textarea></view></block><block wx:if="{{item.tag==='button'}}"><view class="h2w__buttonParent"><button data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></button></view></block><block wx:if="{{item.tag==='checkbox-group'}}"><view class="h2w__checkbox-groupParent"><checkbox-group data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap" catch:change="_change" bindchange="{{item.attrs.bindchange}}"><decode wx:if="{{item.children}}" nodes="{{item}}"/></checkbox-group></view></block><block wx:if="{{item.tag==='checkbox'}}"><view class="h2w__checkboxParent"><checkbox data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap" checked="{{item.attrs.checked}}" value="{{item.attrs.value}}"><decode wx:if="{{item.children}}" nodes="{{item}}"/></checkbox></view></block><block wx:if="{{item.tag==='radio-group'}}"><view class="h2w__radio-groupParent"><radio-group data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap"><decode wx:if="{{item.children}}" nodes="{{item}}"/></radio-group></view></block><block wx:if="{{item.tag==='radio'}}"><view class="h2w__radioParent"><radio data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" id="{{item.attrs.id}}" style="{{item.attrs.style}}" catch:tap="_tap" checked="{{item.attrs.checked}}"><decode wx:if="{{item.children}}" nodes="{{item}}"/></radio></view></block><block wx:if="{{item.tag==='rich-text'}}"><view class="h2w__rich-textParent"><rich-text data-data="{{item}}" class="{{item.attrs.class}}" data="{{item.attrs.data}}" style="{{item.attrs.style}}" catch:tap="_tap" nodes="{{item.children}}"><decode wx:if="{{item.children}}" nodes="{{item}}"/></rich-text></view></block><block wx:if="{{item.tag==='audio-player'}}"><audio-player data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='echarts'}}"><echarts data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='latex'}}"><latex data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='table'}}"><table data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='todogroup'}}"><todogroup data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='yuml'}}"><yuml data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block><block wx:if="{{item.tag==='img'}}"><img data="{{item}}" data-data="{{item}}" catch:tap="_tap"/></block></block>