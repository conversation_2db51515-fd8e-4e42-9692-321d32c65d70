import { defineStore } from 'pinia'
import { ref } from 'vue'
import { request } from '@/services/request'
import { useUserStore } from './user'

export const useApplicationStore = defineStore('application', () => {
  // 应用全局状态
  const isLoading = ref(false)
  const error = ref(null)

  // parameters相关状态
  const parameters = ref({})

  // 获取parameters
  async function fetchParameters() {
    const userStore = useUserStore()
    if (!userStore.checkIsLoggedIn()) {
      return { data: {} }
    }

    error.value = null
    isLoading.value = true

    try {
      const response = await request('/v1/parameters')
      console.log("🚀 ~ fetchParameters ~ response:", response)
      parameters.value = response
      return response
    } catch (err) {
      console.error('获取参数失败:', err)
      // 设置错误状态
      error.value = {
        message: err.message || '获取参数失败',
        code: err.code || 'UNKNOWN_ERROR',
        timestamp: Date.now()
      }
      return { data: {} }
    } finally {
      isLoading.value = false
    }
  }

  // 清空应用数据
  function clearAppData() {
    // 重置所有状态
    parameters.value = {}
    error.value = null
    isLoading.value = false
    console.log('应用全局数据已清空')
  }

  return {
    // 状态
    isLoading,
    error,
    parameters,

    // 方法
    fetchParameters,
    clearAppData
  }
})