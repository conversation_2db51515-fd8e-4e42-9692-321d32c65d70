<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const isDarkMode = ref(false)
const cacheSize = ref('0MB')

// 切换主题
const toggleTheme = () => {
  isDarkMode.value = !isDarkMode.value
  // TODO: 实现主题切换逻辑
}

// 清理缓存
const clearCache = async () => {
  try {
    // TODO: 实现缓存清理逻辑
    uni.showToast({
      title: '缓存清理成功',
      icon: 'success'
    })
  } catch (error) {
    console.error('清理缓存失败:', error)
    uni.showToast({
      title: '清理缓存失败',
      icon: 'error'
    })
  }
}

// 退出登录
const logout = async () => {
  try {
    await userStore.logout()
    uni.reLaunch({
      url: '/pages/login/index'
    })
  } catch (error) {
    console.error('退出登录失败:', error)
    uni.showToast({
      title: '退出登录失败',
      icon: 'error'
    })
  }
}
</script>

<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 设置列表 -->
    <view class="bg-white">
      <!-- 主题设置 -->
      <view class="px-4 py-3 border-b border-gray-100">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <text class="i-ic-carbon-palette text-xl text-gray-600 mr-3"></text>
            <text class="text-base text-gray-800">深色模式</text>
          </view>
          <switch
            :checked="isDarkMode"
            @change="toggleTheme"
            color="#38C948"
          />
        </view>
      </view>

      <!-- 缓存设置 -->
      <view class="px-4 py-3 border-b border-gray-100">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <text class="i-ic-carbon-trash-can text-xl text-gray-600 mr-3"></text>
            <view>
              <text class="text-base text-gray-800 block">清除缓存</text>
              <text class="text-sm text-gray-500">{{ cacheSize }}</text>
            </view>
          </view>
          <view
            class="text-primary"
            @tap="clearCache"
          >
            清理
          </view>
        </view>
      </view>

      <!-- 关于我们 -->
      <view class="px-4 py-3 border-b border-gray-100">
        <view class="flex items-center justify-between">
          <view class="flex items-center">
            <text class="i-ic-carbon-information text-xl text-gray-600 mr-3"></text>
            <text class="text-base text-gray-800">关于我们</text>
          </view>
          <text class="i-ic-carbon-chevron-right text-gray-400"></text>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="px-4 py-8">
      <view
        class="bg-red-50 text-red-500 rounded-lg py-3 text-center"
        @tap="logout"
      >
        退出登录
      </view>
    </view>
  </view>
</template> 