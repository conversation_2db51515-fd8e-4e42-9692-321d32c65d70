<script setup>
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import useMerchantSelect from '@/composables/useMerchantSelect'
import useWechatBinding from '@/composables/useWechatBinding'
import { navigate, navigateBack } from '@/utils/navigate'
import { toast } from '@/utils/interface'

// 用户Store
const userStore = useUserStore()
const isLoggedIn = computed(() => userStore.checkIsLoggedIn())
const userInfo = computed(() => userStore.userInfo || {})
const { isWechatBound, handleGetPhoneNumber, handleWechatUnbindClick } = useWechatBinding()

// 商户选择逻辑
const {
  merchants,
  currentMerchant,
  handleMerchantChange
} = useMerchantSelect()

// 获取当前显示的商户信息
const displayMerchant = computed(() => {
  // 如果有选中的商户，返回该商户
  const selected = merchants.value.find(m => m.merchant_id === currentMerchant.value)
  if (selected) {
    return {
      id: selected.merchant_id,
      label: selected.label
    }
  }
  // 如果没有选中商户，返回默认显示
  return {
    id: null,
    label: '未选择商户'
  }
})

// 商户选择弹窗状态
const showMerchantSelect = ref(false)


// 处理登录点击
const handleLoginClick = () => {
  uni.navigateTo({
    url: '/pages/login/index'
  })
}


const handleLogout = async () => {
  uni.showModal({
    title: '退出登录',
    content: '确定要退出登录吗？',
    success: async (res) => {
      if (res.confirm) {
        await userStore.logout()
        toast('已退出登录', 'success')
        navigateBack()
      }
    }
  })
}


</script>

<template>
  <view class="space-y-20rpx p-2">
    <!-- 用户信息 -->
    <view v-if="isLoggedIn" class="space-y-2 px-2 box-border">
      <view class="text-#999 text-28 py-1 pt-2">用户信息</view>
      <view class="bg-white rounded-20rpx">
        <!-- 用户名 -->
        <view class="flex items-center h-120rpx active:bg-gray-100">
          <view class="w-80rpx h-80rpx flex items-center justify-center mx-2 bg-green-50 bg-opacity-10 rounded-16rpx">
            <view class="i-material-symbols-person-outline text-50rpx text-#232323" />
          </view>
          <view class="flex-1">
            <view class="text-32 text-#232323">用户名</view>
          </view>
          <view class="text-30rpx text-#999 pr-4">{{ userInfo.name || '用户' }}</view>
        </view>

        <!-- 分割线 -->
        <view class="border-b border-b-gray-100 border-b-solid"></view>
        <!-- 手机号 -->
        <view class="flex items-center h-120rpx active:bg-gray-100">
          <view class="w-80rpx h-80rpx flex items-center justify-center mx-2 bg-green-50 bg-opacity-10 rounded-16rpx">
            <view class="i-material-symbols-phone-android-outline text-45rpx text-#232323" />
          </view>
          <view class="flex-1">
            <view class="text-32 text-#232323">手机号</view>
          </view>
          <view class="text-30rpx text-#999 pr-4">{{ userInfo.phone || '未绑定手机号' }}</view>
        </view>
      </view>
    </view>

    <view v-if="isLoggedIn" class="space-y-2 px-2 box-border">
      <view class="text-#999 text-28 py-1">数据管理</view>
      <!-- 商户信息 -->
      <view class="rounded-20rpx bg-white" v-if="merchants.length > 0">
        <view class="flex items-center h-120rpx active:bg-gray-100" @click="showMerchantSelect = true">
          <view class="w-80rpx h-80rpx flex items-center justify-center mx-2 bg-green-50 bg-opacity-10 rounded-16rpx">
            <view class="i-material-symbols-store-outline text-50rpx text-#232323" />
          </view>
          <view class="flex-1">
            <view class="text-32 text-#232323">商户信息</view>
          </view>
          <view class="text-30rpx  text-#999">{{ displayMerchant.label }}</view>
          <view class="i-ic-baseline-chevron-right text-45rpx text-gray-400 ml-2 mr-2" />
        </view>

        <!-- 分割线 -->
        <view class="border-b border-b-gray-100 border-b-solid"></view>
        <view class="flex items-center text-40rpx h-120rpx active:bg-gray-100">
          <view class="w-80rpx h-80rpx flex items-center justify-center mx-2 bg-green-50 bg-opacity-10 rounded-16rpx">
            <view :class="['i-tdesign-logo-wechat-stroke font-400', 'text-45rpx text-#232323']" />
          </view>
          <view class="flex-1">
            <view class="text-32 text-#232323">微信账号绑定</view>
          </view>
          <!--  @click="handleWechatUnbindClick" -->
          <view class="flex items-center" v-if="isWechatBound">
            <view class="text-30rpx  text-#999 pr-4">已绑定</view>
          </view>
          <view v-else class="pr-4">
            <wd-button size="small" type="primary" open-type="getPhoneNumber" @getphonenumber="handleGetPhoneNumber"
              class="!h-56rpx !text-24rpx !px-24rpx">
              绑定微信
            </wd-button>
          </view>
        </view>
      </view>

    </view>


    <!-- 登录按钮 -->
    <view v-if="!isLoggedIn" class="flex justify-center p-40rpx">
      <BaseButton @click="handleLoginClick" class="text-32rpx">
        立即登录
      </BaseButton>
    </view>

    <!-- 底部信息 -->
    <view class="space-y-2 px-2 box-border mt-20rpx">
      <view class="text-#999 text-28 py-1">关于</view>
      <view class="bg-white rounded-20rpx">
        <!-- 用户协议 -->
        <view class="flex items-center h-120rpx active:bg-gray-100"
          @click.capture.stop="navigate('/pages/login/usagePolicy')">
          <view class="w-80rpx h-80rpx flex items-center justify-center mx-2 bg-green-50 bg-opacity-10 rounded-16rpx">
            <view class="i-material-symbols-description-outline text-45rpx text-#232323" />
          </view>
          <view class="flex-1">
            <view class="text-32 text-#232323">用户协议</view>
          </view>
          <view class="i-ic-baseline-chevron-right text-45rpx text-gray-400 ml-2 mr-2" />
        </view>

        <!-- 分割线 -->
        <view class="border-b border-b-gray-100 border-b-solid"></view>
        <!-- 隐私政策 -->
        <view class="flex items-center h-120rpx active:bg-gray-100"
          @click.capture.stop="navigate('/pages/login/privacy')">
          <view class="w-80rpx h-80rpx flex items-center justify-center mx-2 bg-green-50 bg-opacity-10 rounded-16rpx">
            <view class="i-material-symbols-shield-outline text-45rpx text-#232323" />
          </view>
          <view class="flex-1">
            <view class="text-32 text-#232323">隐私政策</view>
          </view>
          <view class="i-ic-baseline-chevron-right text-45rpx text-gray-400 ml-2 mr-2" />
        </view>
      </view>


      <!-- 退出登录 -->
      <view class="bg-white rounded-20rpx !mt-6">
        <view class="flex items-center h-120rpx active:bg-gray-100" @click="handleLogout">
          <view class="w-80rpx h-80rpx flex items-center justify-center mx-2 bg-green-50 bg-opacity-10 rounded-16rpx">
            <view class="i-iconamoon-exit text-50rpx text-#232323" />
          </view>
          <view class="flex-1">
            <view class="text-32 text-#232323">退出登录</view>
          </view>
        </view>
      </view>

      <!-- 版本信息 -->
      <view class="text-center p-20rpx text-30 text-#999 mt-20rpx">
        喂车车智能助手 v0.0.1
      </view>
    </view>

    <!-- 商户选择组件 -->
    <MerchantSelect v-model:value="currentMerchant" v-model:show="showMerchantSelect"
      @select="(merchant) => handleMerchantChange(merchant.merchant_id)" />
  </view>
</template>

<style>
page{
  background:#f5f5f5
}
</style>