/**
 * 对路由的封装是为了可以在视图中直接跳转，不是脱裤子放屁🤣
 */
import { ref } from 'vue';

/**
 * 导航到指定的URL地址
 * @param {string|Object} targetUrl 导航目标的URL地址或者uni-app的路由参数对象
 * @return {Promise} 导航的Promise对象
 */
export function navigate(targetUrl) {
  if (typeof targetUrl === 'string') {
    return uni.navigateTo({
      url: targetUrl
    });
  } else {
    return uni.navigateTo(targetUrl);
  }
}

/**
 * 重定向到指定的URL地址
 * @param {string|Object} targetUrl 重定向目标的URL地址或者uni-app的路由参数对象
 * @return {Promise} 重定向的Promise对象
 */
export function redirect(targetUrl) {
  if (typeof targetUrl === 'string') {
    return uni.redirectTo({
      url: targetUrl
    });
  } else {
    return uni.redirectTo(targetUrl);
  }
}

/**
 * 关闭所有页面，打开应用内的某个页面
 * @param {string|Object} targetUrl 要跳转的页面路径或uni-app的路由参数对象
 * @return {Promise} 跳转的Promise对象
 */
export function reLaunch(targetUrl) {
  if (typeof targetUrl === 'string') {
    return uni.reLaunch({
      url: targetUrl
    });
  } else {
    return uni.reLaunch(targetUrl);
  }
}

/**
 * 关闭当前页面，返回上一页面或多级页面
 * @param {number|Object} [delta] 返回的页面数或uni-app的路由参数对象
 * @return {Promise} 返回的Promise对象
 */
export function navigateBack(delta) {
  if (typeof delta === 'number') {
    return uni.navigateBack({
      delta
    });
  } else {
    return uni.navigateBack(delta);
  }
}


/**
 * 切换到指定的 tab 页面
 * @param {string|Object} url 要切换的页面路径或uni-app的路由参数对象
 * @return {Promise} 返回的 Promise 对象
 */
export function switchTab(url) {
  if (typeof url === 'string') {
    return uni.switchTab({
      url
    });
  } else {
    return uni.switchTab(url);
  }
}