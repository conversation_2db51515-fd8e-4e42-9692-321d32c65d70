<script setup>
import { FEEDBACK_TYPE } from '@/utils/constant/feedback'
import { CHAT_ERROR_MESSAGES } from '@/utils/constant/chat'
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue'
import { request } from '@/services/request'
import { toast } from '@/utils/interface'
import { isEmpty } from 'lodash-es'
// import { formatRelativeTime } from '@/utils/time-formatter' // 不再需要，由父组件处理

const emit = defineEmits(['feedback', 'thinking-updated', 'regenerate', 'edit-message'])
const props = defineProps({
  // 消息类型：user 或 ai
  type: {
    type: String,
    required: true,
    validator: (value) => ['user', 'ai'].includes(value)
  },
  // 消息内容
  message: {
    type: Object,
    default: () => { }
  },
  // 是否显示加载状态
  loading: {
    type: Boolean,
    default: false
  },
  // 格式化后的时间戳
  formattedTime: {
    type: String,
    default: ''
  },
  // 加载耗时
  loadingDuration: {
    type: String,
    default: ''
  },
  // 消息对象中包含 tool 属性，用于显示工具信息
  // message.tool.name - 工具名称
  // message.tool.status - 工具状态（'start' 或 'complete'）
  // --> 更新为 message.tool_calls (数组)，每个元素包含 { id, name, status }
})

// 音量动画状态
const isVolumeAnimating = ref(false)
// 长按提示框状态
const showTooltip = ref(false)
// 长按提示框位置
const tooltipPosition = ref({ x: 0, y: 0 })
// uni-popup 引用
const textSelectPopup = ref(null)

// 检查消息是否为错误消息
const isErrorMessage = computed(() => {
  if (!props.message || !props.message.content) return false;

  // 获取消息内容
  const content = props.message.content;

  // 检查消息内容是否与任何错误消息匹配
  return Object.values(CHAT_ERROR_MESSAGES).includes(content);
})

// 检查是否需要显示骨架屏
const showSkeleton = computed(() => {
  const result = props.type === 'ai' && 
         !props.loading && 
         props.message && 
         props.message.content && 
         !props.message.markdown;
  console.log('showSkeleton:', result, props.message);
  return result;
})

// 移除思考文案相关变量

// 切换音量动画状态并调用文本转语音API
const toggleVolume = async () => {
  try {
    isVolumeAnimating.value = true
    // 调用文本转语音API
    await request('/v1/text-to-audio', {
      method: 'POST',
      header: {
        'Content-Type': 'application/json',
      },
      data: {
        message_id: props.message.id,
      },
      responseType: 'audio/wav'
    })
  } catch (error) {
    console.error('音量控制失败:', error)
    toast('操作失败')
  } finally {
    isVolumeAnimating.value = !isVolumeAnimating.value
  }
}

// 复制内容的具体实现
const copyContent = () => {
  uni.setClipboardData({
    data: props.message.content,
    success: () => {
      toast('复制成功', { icon: 'success', duration: 1500 });
      // 复制成功后关闭提示框
      showTooltip.value = false;
    },
    fail: (e) => {
      console.log('复制失败', e);
      toast('复制失败', { duration: 1500 });
    }
  });
}

// 处理长按事件
const handleLongPress = (event) => {
  // 防止事件冲突
  event.preventDefault();
  event.stopPropagation();

  // 如果消息正在加载中，不显示长按提示框
  if (props.loading) return;

  console.log('触发长按事件');

  // 获取触摸位置
  const touchX = event.touches[0].clientX;
  const touchY = event.touches[0].clientY;

  // 获取屏幕尺寸
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const screenHeight = systemInfo.windowHeight;

  // 计算tooltip的尺寸 (根据列表样式调整)
  const tooltipWidth = 200; // 列表样式的宽度
  // 根据消息类型计算高度
  const tooltipHeight = props.type === 'ai' ? 200 : 100; // AI消息四个选项，用户消息两个选项

  // 计算安全的位置，确保不超出屏幕
  let safeX, safeY;

  // 直接将tooltip放在触摸点位置，只做屏幕边界检查
  // 将tooltip的中心点对齐到触摸点的X坐标
  safeX = touchX - tooltipWidth / 2;

  // 判断触摸点在屏幕的上半部还是下半部
  const isOnTopHalf = touchY < screenHeight / 2;

  if (isOnTopHalf) {
    // 如果触摸点在屏幕上半部，将tooltip放在触摸点下方
    safeY = touchY + 10; // 将tooltip放在触摸点下方，距离触摸点更近
  } else {
    // 如果触摸点在屏幕下半部，将tooltip放在触摸点上方
    safeY = touchY - tooltipHeight - 10; // 将tooltip放在触摸点上方，距离触摸点更近
  }

  // 确保不超出屏幕边界
  // 左边界检查
  if (safeX < 10) {
    safeX = 10;
  }
  // 右边界检查
  if (safeX + tooltipWidth > screenWidth - 10) {
    safeX = screenWidth - tooltipWidth - 10;
  }

  // 上边界检查
  if (safeY < 10) {
    safeY = 10;
  }
  // 下边界检查
  if (safeY + tooltipHeight > screenHeight - 10) {
    safeY = screenHeight - tooltipHeight - 10;
  }

  console.log('触摸点位置:', { x: touchX, y: touchY });
  console.log('计算后的菜单位置:', { x: safeX, y: safeY });

  // 先设置位置，再显示 tooltip
  tooltipPosition.value = {
    x: safeX,
    y: safeY
  };

  // 直接显示 tooltip，不使用 nextTick
  showTooltip.value = true;

  // 添加调试日志
  // console.log('显示 tooltip', {
  //   position: tooltipPosition.value,
  //   bubbleRect: bubbleRect,
  //   showing: showTooltip.value
  // });
}

// 关闭提示框
const closeTooltip = () => {
  console.log('关闭 tooltip');
  showTooltip.value = false;
}

// 打开文本选择弹窗
const openTextSelectPopup = () => {
  // 使用 uni-popup 打开弹窗
  textSelectPopup.value.open('bottom');
  // 关闭 tooltip
  showTooltip.value = false;
}

// 移除思考中文案动画函数

// 监听loading状态变化
watch(() => props.loading, (newValue, oldValue) => {
  // 移除思考文案相关逻辑
  console.log("🚀 ~ watch ~ newValue:", newValue,!oldValue)
  // 当loading状态变化时，触发滚动到底部
  if (newValue && !oldValue && isEmpty(props.message.content)) {
    emit('thinking-updated');
  }
})





// 组件生命周期管理
onMounted(() => {
  // 如果组件加载时已经是loading状态且消息内容为空，触发滚动到底部
  if (props.loading && isEmpty(props.message.content)) {
    emit('thinking-updated');
  }
})



// 监听消息内容变化
watch(() => props.message?.content, () => {
  // 移除思考文案相关逻辑
})



// 组件卸载时清除定时器
onUnmounted(() => {
  // 移除思考文案相关逻辑
})
</script>

<style scoped>
.volume-animation {
  animation: volumePulse 400ms infinite steps(1);
}

@keyframes volumePulse {

  0%,
  100% {
    content: "\e900";
    /* mute icon */
  }

  25% {
    content: "\e901";
    /* down icon */
  }

  50% {
    content: "\e902";
    /* up icon */
  }

  75% {
    content: "\e900";
    /* mute icon */
  }
}

.wave-label {
  position: relative;
}

.wave-bar-1 {
  animation: wave1 0.8s infinite ease-in-out;
  transform-origin: center bottom;
}

.wave-bar-2 {
  animation: wave2 0.9s infinite ease-in-out 0.1s;
  transform-origin: center bottom;
}

.wave-bar-3 {
  animation: wave3 0.7s infinite ease-in-out 0.2s;
  transform-origin: center bottom;
}

.wave-bar-4 {
  animation: wave4 0.85s infinite ease-in-out 0.05s;
  transform-origin: center bottom;
}

.wave-bar-5 {
  animation: wave5 0.75s infinite ease-in-out 0.15s;
  transform-origin: center bottom;
}

@keyframes wave1 {

  0%,
  100% {
    transform: scaleY(0.3);
  }

  50% {
    transform: scaleY(0.8);
  }
}

@keyframes wave2 {

  0%,
  100% {
    transform: scaleY(0.4);
  }

  50% {
    transform: scaleY(0.7);
  }
}

@keyframes wave3 {

  0%,
  100% {
    transform: scaleY(0.2);
  }

  50% {
    transform: scaleY(0.9);
  }
}

@keyframes wave4 {

  0%,
  100% {
    transform: scaleY(0.3);
  }

  50% {
    transform: scaleY(0.6);
  }
}

@keyframes wave5 {

  0%,
  100% {
    transform: scaleY(0.5);
  }

  50% {
    transform: scaleY(0.7);
  }
}

/* 三个圆点波浪动画 */
.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
}

.loading-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.loading-dot:nth-child(1) {
  animation: bounce 0.6s infinite ease-in-out;
  animation-delay: 0s;
}

.loading-dot:nth-child(2) {
  animation: bounce 0.6s infinite ease-in-out;
  animation-delay: 0.1s;
}

.loading-dot:nth-child(3) {
  animation: bounce 0.6s infinite ease-in-out;
  animation-delay: 0.2s;
}

@keyframes bounce {
  0%, 100% {
    opacity: 0.25;
  }
  50% {
    opacity: 0.6;
  }
}

/* Tooltip 动画样式 */
.tooltip-animation {
  opacity: 0;
  animation: tooltipFadeIn 0.15s ease-out forwards;
  transform-origin: center bottom;
  will-change: opacity, transform;
}

@keyframes tooltipFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}
</style>

<template>
  <view>
    <view class="flex" :class="[type === 'user' ? 'justify-end' : 'justify-start']">
      <view>
        <!-- 消息气泡 -->
        <view :class="[
          'max-w-680 rd-3 relative',
          type === 'user' ? 'bg-primary text-white p-3 rd-rt-0 break-anywhere' : 'rd-lt-0 bg-white text-gray-800 overflow-hidden'
        ]" @longpress="handleLongPress">
          <view>
            <!-- 显示工具名称和状态 - 始终显示，不仅在加载状态下 -->
            <slot>
              <!-- 如果需要显示骨架屏 -->
              <template v-if="showSkeleton">
                <view class="p-3">
                  <wd-skeleton theme="paragraph" animation="gradient" />
                </view>
              </template>
              <!-- 如果是towxml对象，使用towxml组件渲染 -->
              <template v-else-if="typeof message.markdown === 'object' && message.markdown !== null">
                <towxml :nodes="message.markdown" class="leading-1.6" />
              </template>
              <!-- 如果是towxml对象，使用towxml组件渲染 -->
              <template v-else-if="typeof message.content === 'object' && message.content !== null">
                <towxml :nodes="message.content" class="leading-1.6" />
              </template>
              <!-- 如果是字符串，直接显示 -->
              <template v-else>
                {{ message.content }}
              </template>
            </slot>
          </view>

          <!-- 操作图标 - 用户消息 -->
          <view v-if="!loading && type === 'user'"
            class="max-w-680 flex justify-end items-center text-white pt-0 text-34">
            <view class="flex gap-2 space-x-2 pt-3">
              <view class=" text-38 i-material-symbols-text-select-end active:text-primary transition-colors duration-200"
                @click.stop="openTextSelectPopup"></view>
              <view class=" text-38 i-material-symbols-edit-square-outline active:text-primary transition-colors duration-200"
                @click.stop="emit('edit-message', message)"></view>
              <view v-if="!isErrorMessage" class=" text-34 i-ic-baseline-content-copy active:text-primary transition-colors duration-200"
                @click.stop="copyContent"></view>
            </view>
          </view>

          <!-- 操作图标 -->
          <view v-if="!loading && type === 'ai'"
            class="max-w-680 flex justify-between items-center text-gray-400 p-3 pt-0 text-34">
            <view class="flex items-center gap-2" :class="{ 'text-primary': isVolumeAnimating }">
              <view v-if="false" class="active:text-primary transition-colors duration-200" @click="toggleVolume">
                <view class="i-ic-round-volume-up"></view>
              </view>
              <view v-if="isVolumeAnimating" class="wave-label text-primary text-sm flex items-center gap-1">
                <view class="wave-bars w-60rpx h-32rpx relative">
                  <view :class="`wave-bar-${index + 1} absolute bottom-0 w-6rpx h-full bg-current rounded-3rpx`"
                    :style="`left:${index * 10}rpx`" v-for="(_, index) in 5" :key="index">
                  </view>
                </view>
              </view>
            </view>
            <view class="flex gap-2 space-x-4">
              <view v-if="message.conversation_id" class="text-40 i-material-symbols-refresh active:text-primary transition-colors duration-200"
                @click="emit('regenerate', message)"></view>
              <view v-if="message.conversation_id" :class="message?.feedback?.rating === FEEDBACK_TYPE.LIKE ? 'text-primary' : ''"
                class=" text-38 i-ic-outline-thumb-up active:text-primary transition-colors duration-200"
                @click="emit('feedback', message?.feedback?.rating === FEEDBACK_TYPE.LIKE ? FEEDBACK_TYPE.CANCEL : FEEDBACK_TYPE.LIKE)">
              </view>
              <view v-if="message.conversation_id" :class="message?.feedback?.rating === FEEDBACK_TYPE.DISLIKE ? 'text-primary' : ''"
                class=" text-38 i-ic-outline-thumb-down active:text-primary transition-colors duration-200"
                @click="emit('feedback', message?.feedback?.rating === FEEDBACK_TYPE.DISLIKE ? FEEDBACK_TYPE.CANCEL : FEEDBACK_TYPE.DISLIKE)">
              </view>
              <view v-if="!isErrorMessage" class=" text-38 i-ic-baseline-content-copy active:text-primary transition-colors duration-200"
                @click="copyContent"></view>
            </view>
          </view>

          <!-- 加载状态 - 显示三个实心圆的波浪动画 -->
          <view v-if="loading">
            <view class="px-3 py-3 flex justify-start items-center">
              <view class="loading-dots">
                <view class="loading-dot bg-primary"></view>
                <view class="loading-dot bg-primary"></view>
                <view class="loading-dot bg-primary"></view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- 消息时间戳或加载耗时 -->
    <view :class="[type === 'user' ? 'justify-end' : 'justify-start']"
      class="flex text-28 text-gray-400 items-center mt-1 px-2">
      <template v-if="message && message.timestamp">
        <text> {{ formattedTime }}</text>
        <template v-if="loadingDuration">
          <text :class="[!!formattedTime?'ml-1':'']">耗时 {{ loadingDuration }}</text>
        </template>
      </template>
    </view>
  </view>
  <!-- 遮罩层，点击关闭提示框 -->
  <view v-if="showTooltip" class="fixed inset-0 z-40" @click="closeTooltip" @touchmove.stop.prevent></view>

  <!-- 长按提示框 -->
  <view v-if="showTooltip" class="fixed z-50 w-200px pointer-events-auto tooltip-animation"
    :style="`top: ${tooltipPosition.y}px; left: ${tooltipPosition.x}px;`" @touchmove.stop.prevent>
    <view class="bg-white rd-lg overflow-hidden" style="box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);">
      <view class="flex flex-col">
        <!-- 复制选项 -->
        <view v-if="!isErrorMessage" @click="copyContent(); closeTooltip();" class="flex items-center p-3 active:bg-gray-100">
          <view class="i-ic-baseline-content-copy text-32rpx mr-3 text-gray-600"></view>
          <text class="text-gray-800 text-28rpx">复制</text>
        </view>

        <!-- 分割线 - 只在显示复制按钮时显示 -->
        <view v-if="!isErrorMessage" class="border-b border-b-gray-100 border-b-solid"></view>

        <!-- 选择文本选项 -->
        <view @click="openTextSelectPopup" class="flex items-center p-3 active:bg-gray-100">
          <view class="i-material-symbols-text-select-end text-32rpx mr-3 text-gray-600"></view>
          <text class="text-gray-800 text-28rpx">选择文本</text>
        </view>

        <!-- 用户消息特有的重新编辑选项 -->
        <template v-if="type === 'user'">
          <view class="border-b border-b-gray-100 border-b-solid"></view>
          <view @click="emit('edit-message', message); closeTooltip();"
            class="flex items-center p-3 active:bg-gray-100">
            <view class="i-material-symbols-edit-square-outline text-32rpx mr-3 text-gray-600"></view>
            <text class="text-gray-800 text-28rpx">重新编辑</text>
          </view>
        </template>

        <!-- AI消息特有的选项 -->
        <template v-if="type === 'ai'">
          <!-- 分割线 -->
          <view class="border-b border-b-gray-100 border-b-solid"></view>

          <!-- 重新生成选项 -->
          <view v-if="message.conversation_id" @click="emit('regenerate', message); closeTooltip();" class="flex items-center p-3 active:bg-gray-100">
            <view class="i-material-symbols-refresh text-36rpx mr-3 text-gray-600"></view>
            <text class="text-gray-800 text-28rpx">重新生成</text>
          </view>

          <!-- 分割线 -->
          <view v-if="message.conversation_id" class="border-b border-b-gray-100 border-b-solid"></view>

          <!-- 喜欢选项 -->
          <view
            v-if="message.conversation_id"
            @click="emit('feedback', message?.feedback?.rating === FEEDBACK_TYPE.LIKE ? FEEDBACK_TYPE.CANCEL : FEEDBACK_TYPE.LIKE); closeTooltip();"
            class="flex items-center p-3 active:bg-gray-100">
            <view :class="message?.feedback?.rating === FEEDBACK_TYPE.LIKE ? 'text-primary' : 'text-gray-600'"
              class="i-ic-outline-thumb-up text-32rpx mr-3"></view>
            <text :class="message?.feedback?.rating === FEEDBACK_TYPE.LIKE ? 'text-primary' : 'text-gray-800'"
              class="text-28rpx">{{ message?.feedback?.rating === FEEDBACK_TYPE.LIKE ? '取消喜欢' : '喜欢' }}</text>
          </view>

          <!-- 分割线 -->
          <view v-if="message.conversation_id" class="border-b border-b-gray-100 border-b-solid"></view>

          <!-- 不喜欢选项 -->
          <view
            v-if="message.conversation_id"
            @click="emit('feedback', message?.feedback?.rating === FEEDBACK_TYPE.DISLIKE ? FEEDBACK_TYPE.CANCEL : FEEDBACK_TYPE.DISLIKE); closeTooltip();"
            class="flex items-center p-3 active:bg-gray-100">
            <view :class="message?.feedback?.rating === FEEDBACK_TYPE.DISLIKE ? 'text-primary' : 'text-gray-600'"
              class="i-ic-outline-thumb-down text-32rpx mr-3"></view>
            <text :class="message?.feedback?.rating === FEEDBACK_TYPE.DISLIKE ? 'text-primary' : 'text-gray-800'"
              class="text-28rpx">{{ message?.feedback?.rating === FEEDBACK_TYPE.DISLIKE ? '取消不喜欢' : '不喜欢' }}</text>
          </view>
        </template>
      </view>
    </view>
  </view>

  <!-- 文本选择弹窗 -->
  <uni-popup ref="textSelectPopup" type="bottom" :safe-area="false" :mask-click="true" :animation="true">
    <view class="h-80vh bg-white flex flex-col rd-t-2">
      <!-- 标题栏 -->
      <view class="flex justify-between items-center p-4 pb-2">
        <text class="text-lg font-bold">选择文本</text>
        <view class="text-18 p-16rpx i-ic-baseline-close" @click="textSelectPopup.close()"></view>
      </view>

      <!-- 滚动内容区域 -->
      <scroll-view scroll-y class="flex-1 px-4 pt-2 w-full box-border h-0" style="height: calc(80vh - 52px);">
        <text class="text-base leading-relaxed text-gray-800" user-select>
          {{ typeof message.content === 'object' && message.content !== null ?
            (message.content._e && message.content._e.text ? message.content._e.text : JSON.stringify(message.content)) :
            message.content }}
        </text>
        <!-- 底部留白空间 -->
        <view class="h-20 w-full"></view>
      </scroll-view>
    </view>
  </uni-popup>
</template>