import qs from 'qs';
import { toast } from '@/utils/interface';
import { getConfig } from '@/config';
import { useUserStore } from '@/stores/user';
/**
 * 此文件的内容均有chatgpt生成
 */
// 添加全局请求拦截器
uni.addInterceptor('request', {
  // 请求拦截器参数
  invoke: options => {
    console.log('=>(http.js:10) 请求信息', options);

    // 获取用户store
    const userStore = useUserStore()
    const isLoggedIn = userStore.checkIsLoggedIn()

    // 处理user参数
    const userValue = userStore.userInfo?.username || ''

    // 初始化data
    if (!options.data) {
      options.data = {};
    }

    // 统一添加user参数
    if (options.method === 'GET') {
      // GET请求处理URL和data中的参数
      const [baseUrl, queryString] = options.url.split('?')
      const urlParams = qs.parse(queryString || '')
      const dataParams = options.data || {}

      // 合并所有参数并添加user
      const mergedParams = {
        ...urlParams,
        ...dataParams,
        user: userValue
      }

      // 更新URL和清空data
      options.url = `${baseUrl}?${qs.stringify(mergedParams)}`
      options.data = null
    } else {
      // POST/PUT等请求将user添加到请求体
      if (typeof options.data === 'object') {
        options.data.user = userValue
      }
    }

    return options;
  },
  success: res => {
    console.log("🚀 ~ success res:", res)
    if (typeof res.data === 'string') {
      try{
        res.data = JSON.parse(res.data);
      }catch(e){
        console.log('=>(http.js:17) 响应数据 JSON 处理错误', res.data);
      }
    }
    // 对响应数据做些什么
    if (res.statusCode !== DataCode.STATUS_SUCCESS &&
        res.statusCode !== DataCode.FORBIDDEN &&
        res.statusCode !== DataCode.UNAUTHORIZED) {
      toast(res.data?.msg || '请求失败: ' + (res.errMsg));
    }
    return res;
  },
  // 请求拦截器异常处理
  fail: err => {
    console.log('=>(http.js:30) 请求错误', err);
    toast({
      title: err.msg || '请求失败: ' + ( err.errMsg),
      icon: 'none'
    });
  }
});
uni.addInterceptor('uploadFile', {
  // 请求拦截器参数
  invoke: options => {
    console.log('=>(http.js:10) 请求信息', options);
    if (!options.formData) {
      options.formData = {};
    }
    return options;
  },
  success: res => {
    console.log('=>(http.js:17) 响应数据1', res.data);
    if (typeof res.data === 'string') {
      res.data = JSON.parse(res.data);
    }
    console.log('=>(http.js:17) 响应数据2', res.data);
    // 对响应数据做些什么
    if (res.statusCode !== DataCode.STATUS_SUCCESS &&
        res.statusCode !== DataCode.UNAUTHORIZED) {
      toast('请求失败: ' + (res.data?.msg || res.errMsg));
    }
    return res;
  },
  // 请求拦截器异常处理
  fail: err => {
    console.log('=>(http.js:30) 请求错误', err);
    toast({
      title: '请求失败: ' + (err.msg || err.errMsg),
      icon: 'none'
    });
  }
});

/**
 * 获取当前环境的API基础URL
 * @returns {string} API基础URL
 */
export function getEnvUrl() {
  return getConfig().BASE_URL;
}

/**
 * 发送GET请求
 * @param {string} url - 请求的URL地址
 * @param {object} [data] - 请求的数据
 * @param {object} [headers] - 自定义请求头配置
 * @returns {Promise<object>} 包含响应数据的Promise对象
 */
export function get(url, data, options = {}) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: getEnvUrl() + url,
      data: data,
      method: 'GET',
      ...options,
      success: function (res) {
        resolve(res.data);
      },
      fail: function (res) {
        reject(res);
      }
    });
  });
}

/**
 * 发送POST请求
 * @param {string} url - 请求的URL地址
 * @param {object} [data] - 请求的数据
 * @param {object} [headers] - 自定义请求头配置
 * @returns {Promise<object>} 包含响应数据的Promise对象
 */
export function post(url, data, options = {}) {
  return new Promise((resolve, reject) => {
    uni.request({
      url: getEnvUrl() + url,
      data: data,
      method: 'POST',
      ...options,
      success: function (res) {
        resolve(res.data);
      },
      fail: function (res) {
        reject(res);
      }
    });
  });
}

/**
 * 上传文件
 * @param {string} url - 上传文件的URL地址
 * @param {string} filePath - 要上传的文件路径
 * @param {object} [formData] - 上传时要附带的数据
 * @returns {Promise<object>} 包含响应数据的Promise对象
 */
export function uploadFile(url, filePath, formData) {
  return new Promise((resolve, reject) => {
    uni.uploadFile({
      url: getEnvUrl() + url,
      filePath: filePath,
      name: 'file',
      formData: formData,
      success: function (res) {
        resolve(res.data);
      },
      fail: function (res) {
        reject(res);
      }
    });
  });
}

/**
 * 表示系统返回的数据编码。
 * @typedef {Object} DataCode
 * @property {number} SUCCESS - 0。表示操作成功。
 * @property {number} STATUS_SUCCESS - 200。表示网络操作成功。
 * @property {number} FORBIDDEN - 403。表示禁止访问。
 * @property {number} UNAUTHORIZED - 401。表示未授权或登录失效。
 */
export const DataCode = {
  STATUS_SUCCESS: 200,
  SUCCESS: 0,
  FORBIDDEN: 403,
  UNAUTHORIZED: 401
};

/**
 * 表示与每个数据编码相关的消息。
 * @typedef {Object} DataCodeMessage
 * @property {string} [0] - 操作成功。
 */
export const DataCodeMessage = {
  [DataCode.SUCCESS]: '操作成功'
};
