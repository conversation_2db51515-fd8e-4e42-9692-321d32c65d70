import { request, parseResponse, API_CONFIG } from './request'
import { getConfig } from '@/config'

// 存储对话ID和父消息ID
let conversation_id = null
let parent_message_id = null

// 创建新对话
export const createNewConversation = () => {
  // 重置对话ID
  conversation_id = null
  parent_message_id = null
}

// 获取对话列表
export const fetchConversations = async (limit = 20, last_id = null) => {
  try {
    console.log('Fetching conversations, limit:', limit, 'last_id:', last_id)
    const queryParams = {
      limit
    }
    if (last_id) {
      queryParams.last_id = last_id
    }

    const queryString = Object.keys(queryParams)
      .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
      .join('&')

    const url = `/v1/conversations?${queryString}`
    console.log(`请求URL: ${url}`)

    const data = await request(url, {
      method: 'GET'
    })
    console.log("🚀 ~ fetchConversations ~ data:", data)
    console.log(`获取到 ${data?.data?.length || 0} 条对话, has_more:`,
      data?.has_more || (data?.data?.length === limit))

    return data.data || []
  } catch (error) {
    console.error('获取对话列表失败:', error)
    return []
  }
}

// 获取指定会话的消息列表
export const getMessages = async (conversationId, first_id = null) => {
  try {
    const queryParams = {
      user: '', // 由request.js统一处理
      conversation_id: conversationId,
      limit: 10  // 默认数量
    }

    // 如果提供了first_id，添加到查询参数中用于分页
    if (first_id) {
      queryParams.first_id = first_id
    }

    const queryString = Object.keys(queryParams)
      .map(key => `${key}=${encodeURIComponent(queryParams[key])}`)
      .join('&')

    const url = `/v1/messages?${queryString}`
    console.log(`获取消息列表: ${url}`)

    const data = await request(url, {
      method: 'GET'
    })

    return data
  } catch (error) {
    console.error(`获取对话 ${conversationId} 的消息失败:`, error)
    throw error  // 向上传递错误，便于处理
  }
}

// 发送聊天消息
export const sendChatMessage = async (query, options = {}) => {
  try {
    // 构建请求参数
    const payload = {
      inputs: {},
      query,
      response_mode: options.response_mode || API_CONFIG.DEFAULT_PARAMS.response_mode,
      files: options.files || []
    }

    // 如果有对话ID和父消息ID，添加到请求中
    if (conversation_id) {
      payload.conversation_id = conversation_id
    }

    if (parent_message_id) {
      payload.message_id = parent_message_id
    }

    console.log('Sending message:', payload)

    const data = await request('/v1/chat-messages', {
      method: 'POST',
      data: payload,
      enableChunked: options.streaming,
      responseType: options.responseType
    })

    // 如果是流式响应，直接返回
    if (options.streaming || options.responseType === 'arraybuffer') {
      return data
    }

    // 解析响应
    const result = parseResponse(data)

    if (result) {
      // 更新对话ID和父消息ID，用于下一次请求
      if (result.conversation_id) {
        conversation_id = result.conversation_id
      }

      if (result.messageId) {
        parent_message_id = result.messageId
      }

      return result.content
    }

    return null
  } catch (error) {
    console.error('发送消息失败:', error)
    throw error
  }
}

// 更新API配置
export const updateApiConfig = (config) => {
  Object.assign(API_CONFIG, config)
}

// ===== 用户认证相关API =====

/**
 * 用户登录
 * @param {String} username 用户名
 * @param {String} password 密码
 * @returns {Promise<Object>} 登录结果，包含token和用户信息
 */
export const login = async (username, password) => {
  try {
    const data = await request('/api/auth/login', {
      method: 'POST',
      data: {
        username,
        password
      }
    })

    return data
  } catch (error) {
    console.error('登录失败:', error)
    throw error
  }
}

/**
 * 用户注册
 * @param {String} username 用户名
 * @param {String} password 密码
 * @returns {Promise<Object>} 注册结果
 */
export const register = async (username, password) => {
  try {
    const data = await request('/api/auth/register', {
      method: 'POST',
      data: {
        username,
        password
      }
    })

    return data
  } catch (error) {
    console.error('注册失败:', error)
    throw error
  }
}

/**
 * 获取用户信息
 * @returns {Promise<Object>} 用户信息
 */
export const getUserInfo = async () => {
  try {
    const data = await request('/api/user/info', {
      method: 'GET'
    })

    return data
  } catch (error) {
    console.error('获取用户信息失败:', error)
    throw error
  }
}

/**
 * 更新用户信息
 * @param {Object} userInfo 用户信息
 * @returns {Promise<Object>} 更新结果
 */
export const updateUserInfo = async (userInfo) => {
  try {
    const data = await request('/api/user/info', {
      method: 'PUT',
      data: userInfo
    })

    return data
  } catch (error) {
    console.error('更新用户信息失败:', error)
    throw error
  }
}

/**
 * 微信登录
 * @param {String} code 微信授权码
 * @returns {Promise<Object>} 登录结果
 */
export const wxLogin = async (code) => {
  try {
    const data = await request('/api/auth/wxlogin', {
      method: 'POST',
      data: {
        code
      }
    })

    return data
  } catch (error) {
    console.error('微信登录失败:', error)
    throw error
  }
}

/**
 * 修改密码
 * @param {String} oldPassword 旧密码
 * @param {String} newPassword 新密码
 * @returns {Promise<Object>} 修改结果
 */
export const changePassword = async (oldPassword, newPassword) => {
  try {
    const data = await request('/api/user/change-password', {
      method: 'POST',
      data: {
        old_password: oldPassword,
        new_password: newPassword
      }
    })

    return data
  } catch (error) {
    console.error('修改密码失败:', error)
    throw error
  }
}

// 用户登出
export const logout = async (token) => {
  try {
    const data = await request('/v1/logout', {
      method: 'POST',
      data: { token }
    })
    return data
  } catch (error) {
    console.error('登出失败:', error)
    throw error
  }
}

// ===== 商户相关API =====

/**
 * 切换当前商户
 * @param {Object} params 商户切换参数
 * @param {String} params.merchant_id 商户ID
 * @param {Number} [params.merchant_type=1] 商户类型，默认为1(油站)
 * @returns {Promise<Object>} 切换结果
 */
export const switchMerchant = async (params) => {
  try {
    const data = await request('/v1/switchOstn', {
      method: 'POST',
      data: {
        merchant_id: params.merchant_id,
        merchant_type: params.merchant_type || 1
      }
    })
    return data
  } catch (error) {
    console.error('切换商户失败:', error)
    throw error
  }
}

// 导出API服务
export default {
  sendChatMessage,
  fetchConversations,
  getMessages,
  updateApiConfig,
  createNewConversation,
  // 用户认证相关
  login,
  register,
  getUserInfo,
  updateUserInfo,
  wxLogin,
  changePassword,
  logout,
  // 商户相关
  switchMerchant
}