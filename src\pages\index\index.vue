<script setup>
// Multi-modal input components removed
import RecommendedQuestions from '@/components/RecommendedQuestions/RecommendedQuestions.vue'
import useMerchantSelect from '@/composables/useMerchantSelect'
import useWechatBinding from '@/composables/useWechatBinding'
import { getConfig } from '@/config'
import { request } from '@/services/request'
import { useChatStore } from '@/stores/chat'
import { useHistoryStore } from '@/stores/history'
import { useUserStore } from '@/stores/user'
import { useApplicationStore } from '@/stores/application'
import { navigate } from '@/utils/navigate'
import { toast } from '@/utils/interface'
import { formatRelativeTime } from '@/utils/time-formatter'
import { onReady, onShow, onHide, onUnload } from '@dcloudio/uni-app'
import { throttle } from 'lodash-es'
import { storeToRefs } from 'pinia'
import { nextTick, ref, computed,watch  } from 'vue'
import { parseMessageContent, parseAgentThoughts } from '@/utils/message-parser'
// 引入 towxml
const towxmlInstance = require('../../wxcomponents/towxml/index.js');
import { TEMP_ID, CHAT_ERROR_MESSAGES } from '@/utils/constant/chat'

const chatStore = useChatStore()
const userStore = useUserStore()
const historyStore = useHistoryStore()
const appStore = useApplicationStore()
const { messages, isLoading } = storeToRefs(chatStore)
const { error: appError } = storeToRefs(appStore)


// 定义定时更新相对时间常量 (30秒)
const MESSAGE_TIME_UPDATE_INTERVAL = 30000

const messageRelativeTime = ref({})
let messageTimeUpdateTimer = null

// 加载耗时相关变量
const loadingDuration = ref('0.0s') // 当前加载耗时
let loadingDurationTimer = null // 加载耗时更新定时器

// 更新所有消息的相对时间
function handleMessageRelativeTime() {
  // 如果没有消息，则不需要更新
  if (!messages?.value?.length) {
    return;
  }
  // 更新每条消息的相对时间
  messages.value.forEach(item => {
    // 确保每条消息都有时间戳
    if (!item.timestamp) {
      item.timestamp = Date.now();
    }
    if(item.id.includes(TEMP_ID)){
      return;
    }
    messageRelativeTime.value[item.id] = formatRelativeTime(item.timestamp)
  })
  console.log('已更新所有消息的相对时间')
}

// 更新加载耗时
function updateLoadingDuration() {
  // 获取最后一条AI消息
  const lastAiMessage = messages.value.filter(m => m.type === 'ai').pop();

  if (lastAiMessage) {
    // 找到最后一条AI消息之前的最近一条用户消息
    const messagesArray = messages.value;
    let userMessageIndex = -1;

    // 从后向前查找，找到最后一条AI消息的位置
    const aiMessageIndex = messagesArray.findIndex(m => m.id === lastAiMessage.id);

    if (aiMessageIndex > 0) {
      // 从AI消息位置向前查找最近的用户消息
      for (let i = aiMessageIndex - 1; i >= 0; i--) {
        if (messagesArray[i].type === 'user') {
          userMessageIndex = i;
          break;
        }
      }
    }

    // 确定起始时间戳
    let startTime;
    if (userMessageIndex !== -1 && messagesArray[userMessageIndex].timestamp) {
      // 如果找到了用户消息，使用用户消息的时间戳作为起始时间
      startTime = messagesArray[userMessageIndex].timestamp;
    } else if (lastAiMessage.timestamp) {
      // 如果没有找到用户消息，回退到使用AI消息的时间戳
      startTime = lastAiMessage.timestamp;
    } else {
      // 如果都没有时间戳，则不更新
      return;
    }

    // 计算持续时间
    const currentTime = Date.now();
    const duration = (currentTime - startTime) / 1000;

    // 格式化时间显示，超过60秒显示为分钟和秒
    if (duration >= 60) {
      const minutes = Math.floor(duration / 60);
      const seconds = Math.floor(duration % 60);
      loadingDuration.value = `${minutes}分${seconds}秒`;
    } else {
      loadingDuration.value = `${duration.toFixed(1)}秒`;
    }
  }
}

// 监听loading状态变化
watch(() => isLoading.value, (newValue, oldValue) => {
  if (newValue && !oldValue) {
    // loading状态从false变为true，开始计时
    if (!loadingDurationTimer) {
      loadingDurationTimer = setInterval(updateLoadingDuration, 100);
      // 立即更新一次
      updateLoadingDuration();
    }
  } else if (!newValue && oldValue) {
    // loading状态从true变为false，停止计时器但保留耗时显示
    if (loadingDurationTimer) {
      clearInterval(loadingDurationTimer);
      loadingDurationTimer = null;
    }
    // 最后更新一次，确保显示最终的耗时
    updateLoadingDuration();
  }
})

// 微信绑定功能
const {
  isWechatBound,
  handleGetPhoneNumber,
  setDismissBindingPrompt,
  getDismissBindingPrompt
} = useWechatBinding()

const isBindingPromptDismissed = ref(getDismissBindingPrompt())
const showBindingPrompt = computed(() => !isWechatBound.value && !isBindingPromptDismissed.value)

const showCloseModal = () => {
  uni.showModal({
    title: '提示',
    content: '关闭后可以去个人中心绑定',
    confirmText: '知道了',
    cancelText: '取消',
    success: (res) => {
      if (res.confirm) {
        setDismissBindingPrompt(true)
        isBindingPromptDismissed.value = true
      }
    }
  })
}

const { token, merchantInfo } = storeToRefs(userStore)
const isLoggedIn = computed(() => userStore.checkIsLoggedIn())
console.log("🚀 ~ merchantInfo:", merchantInfo, isLoggedIn, token)

// 从history store获取messagesHasMore
const { messagesHasMore } = storeToRefs(historyStore)

// 消息分页相关状态
const isLoadingMoreMessages = ref(false)
const firstMessageId = ref(null)

// 商户选择逻辑
const {
  currentMerchant: currentMerchantId,
  formattedMerchants,
  handleMerchantChange
} = useMerchantSelect()
const {
  addUserMessage,
  addAIMessage,
  setLoading,
  saveHistoryMessages,
  clearMessages
} = chatStore

const inputContent = ref('')
const scrollRef = ref(null)
const currentRequestTask = ref(null)
const isRequestFail = ref(false)
const currentTaskId = ref(null)
const navbarHeight = ref(0) // navbar高度变量

// 判断输入框是否为空，用于控制自定义placeholder显示
const isInputEmpty = computed(() => !inputContent.value)

getConfig();

// 当前会话ID
const currentConversationId = ref(null);

// 控制标题下方标签的状态
const isTitlePopupVisible = ref(false);

// 切换标题下方标签显示/隐藏的方法
const toggleTitlePopup = () => {
  // 如果已经打开，则关闭
  if (isTitlePopupVisible.value) {
    isTitlePopupVisible.value = false;
    return;
  }

  // 关闭其他所有弹出层
  isMerchantSelectVisible.value = false;
  if (moreOptionsRef.value) moreOptionsRef.value.close();

  // 打开标题下拉菜单
  isTitlePopupVisible.value = true;
};

// 是否显示商户选择
const isMerchantSelectVisible = ref(false);

// 切换商户选择显示/隐藏的方法
const toggleMerchantSelect = () => {
  // 如果已经打开，则关闭
  if (isMerchantSelectVisible.value) {
    isMerchantSelectVisible.value = false;
    return;
  }

  // 关闭其他所有弹出层
  isTitlePopupVisible.value = false;
  if (moreOptionsRef.value) moreOptionsRef.value.close();

  // 打开商户选择菜单
  isMerchantSelectVisible.value = true;
};

// 弹出菜单选项数组
const titleMenuOptions = ref([
  { id: 'new', label: '新建会话', icon: 'i-ic-round-add-circle-outline', action: handleNewSession },
  // {
  //   id: 'voice',
  //   label: '语音播放',
  //   icon: 'i-ic-outline-volume-up',
  //   action: null,
  //   showSwitch: true
  // },
  // {
  //   id: 'merchant',
  //   label: '切换商户',
  //   icon: 'i-ic-outline-store',
  //   action: () => toggleMerchantSelect(),
  //   showCurrentValue: true
  // }
]);

// 获取当前选中的商户对象
const currentMerchantObject = computed(() => {
  if (!formattedMerchants.value || !formattedMerchants.value.length) return null;
  return formattedMerchants.value.find(m => m.value === currentMerchantId.value);
});

// 处理菜单项点击
const handleMenuItemClick = (option) => {
  if (isLoading.value) {
    toast('对话进行中，请稍后…');
    return;
  }

  if (option.action && typeof option.action === 'function') {
    option.action();
  }

  isTitlePopupVisible.value = false;
};

// 创建节流更新UI消息的函数，每100毫秒最多执行一次
// 这样可以减少对页面的频繁更新，提高性能
// 同时用户仍能近实时看到后端返回的消息，而不需要等到整个请求完成
const updateUIMessage = (message, content) => {
  // 打印调试信息
  // console.log("🔄 更新UI消息:", content.substring(content.length - 20));

  // 直接修改引用可能不会触发响应式更新，使用Vue的响应式API确保更新
  // 先创建一个新对象
  const updatedMessage = { ...message, content };

  // 查找消息在数组中的索引
  const index = messages.value.findIndex(m => m.id === message.id);
  if (index !== -1) {
    // 替换整个对象以确保响应式更新
    messages.value.splice(index, 1, updatedMessage);
    // console.log("💾 消息已更新，ID:", message.id);
  } else {
    // 如果找不到消息，直接设置内容
    message.content = content;
    console.log("⚠️ 未找到消息对象，直接更新内容");
  }

  // 强制刷新UI
  scrollToBottom();
}

// 使用throttle包装函数，确保100毫秒内最多执行一次
// 这样即使后端返回很多小块数据，也不会造成UI卡顿
const throttledUpdateUIMessage = throttle(updateUIMessage, 50, { leading: true, trailing: true })

const textEditorPopupRef = ref(null)
// 多模态输入相关变量已移除

// 跟踪已显示的消息内容
const displayedContent = ref('')
// 跟踪最后一条消息的时间戳，用于确保消息按时间顺序显示
const lastMessageTimestamp = ref(0)

// 历史记录抽屉相关 - 修改为使用 ref 引用 uni-popup 组件
const moreOptionsRef = ref(null)
const moreOptionsComponent = ref(null)
// 推荐问题弹出层引用
const recommendedQuestionsRef = ref(null)

// 解析Markdown表格相关功能已移除
// 确保有一个AI消息存在 - 全局可用的辅助函数
const ensureAIMessage = () => {
  // console.log("🔍 检查AI消息容器");

  const lastMessage = messages.value[messages.value.length - 1];

  // 如果没有消息或最后一条不是AI消息，则添加一个新的AI消息
  if (!lastMessage || lastMessage.type !== 'ai') {
    // console.log("➕ 创建新的AI消息容器");
    addAIMessage('');

    // 更新消息相对时间
    handleMessageRelativeTime()

    // 强制触发渲染和滚动
    nextTick(() => {
      scrollToBottom();
    });

    return messages.value[messages.value.length - 1];
  }
  return lastMessage;
};

const currentMessage = ref('')

const stopMessageIds = ref([])

// 跟踪工具调用状态
const toolCallsMap = ref({}) // 用于存储工具调用的状态，key 是 tool_id，value 是工具调用的状态

// 处理流式数据
const handleChunkData = (res) => {
  // console.log("🚀 ~ handleChunkData ~ 收到数据块:", res)
  try {
    let rawStr = "";

    // 直接将ArrayBuffer转换为字符串
    rawStr = Array.from(new Uint8Array(res.data))
      .map((byte) => String.fromCharCode(byte))
      .join("");

    // 处理中文编码
    rawStr = String(rawStr);
    if (currentMessage.value) {
      rawStr = currentMessage.value + rawStr;
      currentMessage.value = '';
      // console.log('保存无法解析的JSON 0', rawStr)
    }

    // console.log("🚀 ~ handleChunkData ~ 解码后的数据:", rawStr);

    // 尝试解析数据
    try {
      // 处理可能的多行数据
      const lines = rawStr.split('\n').filter(line => line.trim());

      for (const line of lines) {
        // 移除 "data: " 前缀
        const jsonStr = line.replace(/^data:\s*/, '').trim();
        console.log("🚀 ~ handleChunkData ~ jsonStr:", jsonStr)

        if (!jsonStr) continue;

        // 特殊处理ping事件
        if (jsonStr === 'event: ping') {
          // console.log("🚀 ~ handleChunkData ~ 收到ping事件");
          continue;
        }

        // console.log("🚀 ~ handleChunkData ~ 解析JSON:", jsonStr);

        let rawData;
        try {
          // 使用正则表达式判断是否为JSON格式
          rawData = JSON.parse(jsonStr);
        } catch (parseErr) {
          // console.error("JSON解析失败:", parseErr, "原始数据:", line);
          if (jsonStr.includes('event') && jsonStr.includes('error')) {
            rawData = {
              event: 'error',
              message: jsonStr
            }
          } else {
            currentMessage.value = line;
            // console.log('保存无法解析的JSON 1', currentMessage.value)
            continue;
          }
        }

        // console.log('当前会任务ID', rawData.task_id,rawData.message_id)
        if (rawData.task_id && stopMessageIds.value.includes(rawData.task_id)) {
          console.log('当前会任务ID stopMessageIds 收到停止消息，跳过', stopMessageIds.value, rawData.task_id)
          continue;
        }

        // console.log('rawData',rawData)
        // 获取task_id
        if (rawData.task_id && !currentTaskId.value) {
          // console.log("🚀 ~ handleChunkData ~ 获取到task_id:", rawData.task_id);
          currentTaskId.value = rawData.task_id;
        }

        // 处理会话创建
        if (rawData.conversation_id) {
          // 如果是第一次获取conversation_id，则保存到变量
          if(!currentConversationId.value){
            currentConversationId.value = rawData.conversation_id;
            console.log("🚀 保存当前会话ID:", currentConversationId.value);
          }
          // 获取最后一个AI消息并增加 conversation_id 属性
          const lastAIMessage = ensureAIMessage();
          if (lastAIMessage?.conversation_id !== rawData.conversation_id) {
            lastAIMessage.conversation_id = rawData.conversation_id;
            console.log("🚀 为最后一个AI消息增加 conversation_id:", lastAIMessage.id, lastAIMessage.conversation_id);
            historyStore.handleConversationCreated(rawData);
          }
        }

        // 处理不同类型的事件
        if (rawData.event) {
          // 处理所有事件类型
          // console.log(`🚀 ~ handleChunkData ~ 处理事件: ${rawData.event}`);

          // 处理错误事件
          if (rawData.event === "error") {
            console.error("收到错误事件:", rawData);

            try {
              // 获取AI消息容器
              const lastMessage = ensureAIMessage();

              // 使用常量中的标准错误消息
              const userFriendlyError = CHAT_ERROR_MESSAGES.SERVER_BUSY;

              // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
              if (lastMessage.content && lastMessage.content.trim()) {
                lastMessage.content = lastMessage.content + "\n" + userFriendlyError;
              } else {
                lastMessage.content = userFriendlyError;
              }

              displayedContent.value = lastMessage.content;
              lastMessage.markdown = towxmlInstance(lastMessage.content, 'markdown', {
                theme: 'light', // 根据需要设置主题
                events: { /* 事件处理通常在渲染器中 */ }
              });

              // 强制刷新UI
              nextTick(() => {
                scrollToBottom();
              });

              // 结束加载状态
              setLoading(false);
              saveHistoryMessages();
            } catch (err) {
              console.error("处理error事件出错:", err);
              // 尝试恢复
              addAIMessage("服务器繁忙，请稍后重试。");
            }
            continue;
          }

          // 处理message事件 - 完全重写处理逻辑
          if (['agent_message', 'message'].includes(rawData.event)) {
            // NEW: Logic to mark all preceding 'start' tools as 'complete'
            // when a user-facing message content starts to arrive.
            let potentialMessageContent = rawData.answer || rawData.content || rawData.message || rawData.text || rawData.data?.content || '';
            // END OF NEW LOGIC

            // 提取消息内容，兼容不同的字段名
            let messageContent = potentialMessageContent; //rawData.answer || rawData.content || rawData.message || rawData.text || rawData.data?.content || '';
            // console.log("🚀 ~ handleChunkData ~ newContent messageContent:", messageContent)

            // 检查是否存在action_input属性
            messageContent = parseMessageContent(messageContent);

            // 如果answer为空，尝试从agent_thoughts中获取所有thought内容并按顺序拼接
            if (!messageContent && rawData.agent_thoughts && rawData.agent_thoughts.length > 0) {
              messageContent = parseAgentThoughts(rawData.agent_thoughts);
              if (messageContent) {
                console.log("🚀 ~ handleChunkData ~ 使用处理后的agent_thoughts作为消息内容");
              }
            }

            // console.log("🚀 ~ handleChunkData ~ 收到消息:", messageContent);

            // 如果没有消息内容，记录日志但不退出处理
            if (!messageContent) {
              console.warn("收到空消息内容，原始数据:", rawData);
            }

            try {
              // 获取AI消息容器
              const lastMessage = ensureAIMessage();

              // 准备新内容
              const newContent = parseMessageContent((lastMessage.content || '') + messageContent);
              // console.log("🚀 ~ handleChunkData ~ newContent:", newContent)
              displayedContent.value = newContent;

              // 过滤 <think> 等标签
              const filteredContent = newContent
                .replace(/<think[\s\S]*?<\/think>/g, '')
                .replace(/<th[\s\S]*?<\/th/g, '')
                .replace(/<think[\s\S]*?<\/th/g, '')
                .replace(/<th[\s\S]*?<\/think>/g, '')
                .replace(/<(think|th)[\s\S]*$/g, '');

              try {
                // 尝试解析 Markdown 内容
                const parsedResult = towxmlInstance(filteredContent, 'markdown', {
                  theme: 'light',
                  events: { /* 事件处理通常在渲染器中 */ }
                });

                // 直接使用响应式更新
                const updatedMessage = { ...lastMessage, content: filteredContent, markdown: parsedResult };
                const index = messages.value.findIndex(m => m.id === lastMessage.id);
                if (index !== -1) {
                  updatedMessage.id = rawData.id;
                  // console.log("📝 直接更新消息内容，ID:", lastMessage.id);
                  messages.value.splice(index, 1, updatedMessage);

                  // 刷新UI，尽量保持用户的滚动位置
                  nextTick(() => {
                    scrollToBottom();
                  });
                } else {
                  // 如果找不到消息，使用节流函数
                  console.log("⚠️ 未找到消息对象，使用节流函数更新");
                  throttledUpdateUIMessage(lastMessage, parsedResult);
                }
              } catch (error) {
                console.error('解析 Markdown 失败:', error);
                // 解析失败时使用原始文本
                const updatedMessage = { ...lastMessage, content: newContent };
                const index = messages.value.findIndex(m => m.id === lastMessage.id);
                if (index !== -1) {
                  updatedMessage.id = rawData.id;
                  messages.value.splice(index, 1, updatedMessage);
                  nextTick(() => {
                    scrollToBottom();
                  });
                } else {
                  throttledUpdateUIMessage(lastMessage, newContent);
                }
              }

              // 直接打印到控制台以便调试
              // console.log("🚀 ~ 更新后的消息内容:", newContent);
            } catch (err) {
              console.error("处理message事件出错:", err);

              // 尝试恢复
              try {
                addAIMessage(messageContent || '');
                // 错误恢复时仍然尊重用户的滚动位置
                nextTick(() => {
                  scrollToBottom();
                });
              } catch (recoverErr) {
                console.error("恢复失败:", recoverErr);
              }
            }
            continue;
          }

          // 节点开始事件
          if (rawData.event === "node_started") {
            // 只要有节点开始，确保AI消息已经创建
            try {
              ensureAIMessage();
              // 节点开始时，尽量保持用户的滚动位置
              nextTick(() => {
                scrollToBottom();
              });
            } catch (err) {
              console.error("处理node_started事件出错:", err);
            }
            continue;
          }

          // 节点完成事件
          if (rawData.event === "node_finished") {
            // 如果是回答类型节点，提取答案
            if (rawData.data && rawData.data.node_type === "answer") {
              const answer = rawData.data.outputs?.answer || '';

              if (answer) {
                // console.log("🚀 ~ handleChunkData ~ 节点答案:", answer);
                try {
                  const lastMessage = ensureAIMessage();

                  // 累积内容，而不是替换
                  const currentContent = lastMessage.content || '';
                  if (!currentContent.includes(answer)) {
                    // 准备新内容
                    const newContent = currentContent ? currentContent + answer : answer;
                    displayedContent.value = newContent;

                    // 使用节流函数更新UI
                    throttledUpdateUIMessage(lastMessage, newContent);
                  }
                } catch (err) {
                  console.error("处理node_finished事件出错:", err);
                }
              }
            }
            continue;
          }

          // 处理 agent_thought 事件
          if (rawData.event === "agent_thought") {
            console.log("🚀 ~ handleChunkData ~ 收到 agent_thought 事件:", rawData);
            const rawToolString = rawData.tool || '';
            const toolId = rawData.id || ''; // This is the specific tool_call_id for the entire thought
            const toolInputStr = rawData.tool_input;
            let parsedToolInput = null;
            if (!rawToolString) {
              console.log('跳出空工具')
              continue;
            }

            // --- 新增日志：检查是否有 observation 字段 ---
            if (rawData.observation) {
              console.log(`[agent_thought DEBUG] ID: ${toolId}, Position: ${rawData.position}, Tool: ${rawData.tool}, Observation: ${rawData.observation.substring(0, 200)}...`);

              // 检查是否包含错误信息
              const isError = typeof rawData.observation === 'string' &&
                (rawData.observation.includes('error') ||
                  rawData.observation.includes('Error') ||
                  rawData.observation.includes('ERROR') ||
                  rawData.observation.includes('failed') ||
                  rawData.observation.includes('Failed') ||
                  rawData.observation.includes('FAILED') ||
                  rawData.observation.includes('exception') ||
                  rawData.observation.includes('Exception'));

              if (isError) {
                console.log(`[agent_thought] Detected error in observation: ${toolId}`, JSON.stringify(toolCallsMap.value));
                // 更新工具状态为错误
                if (toolCallsMap.value[toolId]) {
                  toolCallsMap.value[toolId].status = 'error';
                  toolCallsMap.value[toolId].error = rawData.observation;

                  // 更新UI中的工具状态
                  const lastMessage = ensureAIMessage();
                  if (lastMessage.tool_calls && Array.isArray(lastMessage.tool_calls)) {
                    const toolCall = lastMessage.tool_calls.find(tc => tc.id === toolId);
                    if (toolCall) {
                      toolCall.status = 'error';
                      toolCall.error = rawData.observation;

                      // 更新文本表示
                      if (typeof lastMessage.content === 'string') {
                        // 替换 calltool.start.{id} 为 calltool.error.{id}，并添加错误信息
                        const startPattern = new RegExp(`\`\`\`calltool\\.start\\.${toolId}\\s*\\n\\s*${toolCall.name}\\s*\\n\\s*\`\`\``, 'g');
                        // 提取简短的错误信息
                        let errorSummary = rawData.observation;
                        if (errorSummary && errorSummary.length > 100) {
                          errorSummary = errorSummary.substring(0, 100) + '...';
                        }
                        lastMessage.content = lastMessage.content.replace(startPattern,
                          `\`\`\`calltool.error.${toolId}\n${toolCall.name}\n${errorSummary}\n\`\`\``);
                      }
                      console.log('更新工具content 状态变更', lastMessage.content)
                      lastMessage.markdown = towxmlInstance(lastMessage.content, 'markdown', {
                        theme: 'light', // 根据需要设置主题
                        events: { /* 事件处理通常在渲染器中 */ }
                      });

                      // 更新UI
                      const index = messages.value.findIndex(m => m.id === lastMessage.id);
                      if (index !== -1) {
                        const updatedMessage = { ...lastMessage };
                        messages.value.splice(index, 1, updatedMessage);
                        nextTick(() => scrollToBottom());
                      }
                    }
                  }
                }
              } else {
                // 工具调用成功，更新状态为 complete
                console.log(`[agent_thought] Tool execution completed successfully: ${toolId}`);

                // 更新工具状态为完成
                if (toolCallsMap.value[toolId]) {
                  toolCallsMap.value[toolId].status = 'complete';

                  // 更新UI中的工具状态
                  const lastMessage = ensureAIMessage();
                  if (lastMessage.tool_calls && Array.isArray(lastMessage.tool_calls)) {
                    const toolCall = lastMessage.tool_calls.find(tc => tc.id === toolId);
                    if (toolCall) {
                      toolCall.status = 'complete';

                      // 更新文本表示
                      if (typeof lastMessage.content === 'string') {
                        // 替换 calltool.start.{id} 为 calltool.complete.{id}
                        const startPattern = new RegExp(`\`\`\`calltool\\.start\\.${toolId}\\s*\\n\\s*${toolCall.name}\\s*\\n\\s*\`\`\``, 'g');
                        lastMessage.content = lastMessage.content.replace(startPattern,
                          `\`\`\`calltool.complete.${toolId}\n${toolCall.name}\n\`\`\``);
                      }
                      console.log('更新工具content 状态变更', lastMessage.content)
                      lastMessage.markdown = towxmlInstance(lastMessage.content, 'markdown', {
                        theme: 'light', // 根据需要设置主题
                        events: { /* 事件处理通常在渲染器中 */ }
                      });

                      // 更新UI
                      const index = messages.value.findIndex(m => m.id === lastMessage.id);
                      if (index !== -1) {
                        const updatedMessage = { ...lastMessage };
                        messages.value.splice(index, 1, updatedMessage);
                        nextTick(() => scrollToBottom());
                      }
                    }
                  }
                }
              }
              continue;
            }
            // --- 结束新增日志 ---

            try {
              if (toolInputStr) {
                try {
                  parsedToolInput = JSON.parse(toolInputStr);
                } catch (e) {
                  console.error("[agent_thought] Failed to parse rawData.tool_input JSON:", e, toolInputStr);

                  // 标记为解析错误
                  if (toolCallsMap.value[toolId]) {
                    toolCallsMap.value[toolId].status = 'error';
                    toolCallsMap.value[toolId].error = `JSON解析错误: ${e.message}`;

                    // 更新UI中的工具状态
                    const lastMessage = ensureAIMessage(); // Keep this outside the if
                    if (lastMessage.tool_calls && Array.isArray(lastMessage.tool_calls)) {
                      // 查找所有匹配的工具调用
                      const matchingToolCalls = lastMessage.tool_calls.filter(tc => tc.id === toolId);
                      if (matchingToolCalls.length > 0) {
                        // 更新每个匹配工具调用的状态
                        matchingToolCalls.forEach(toolCall => {
                          toolCall.status = 'error';
                          toolCall.error = `JSON解析错误: ${e.message}`;
                        });

                        // 更新文本表示 (只需要替换一次状态标记)
                        if (typeof lastMessage.content === 'string') {
                          // Replace the start marker with error marker
                          const startPattern = new RegExp(`\`\`\`calltool\\.start\\.${toolId}\\s*\\n`, 'g');
                          lastMessage.content = lastMessage.content.replace(startPattern, `\`\`\`calltool.error.${toolId}\n`);
                        }

                        // 更新UI (通过替换整个消息对象触发响应式更新)
                        const index = messages.value.findIndex(m => m.id === lastMessage.id);
                        if (index !== -1) {
                          const updatedMessage = { ...lastMessage };
                          messages.value.splice(index, 1, updatedMessage);
                          nextTick(() => scrollToBottom());
                        }
                      }
                    }
                  }

                  // Proceed without parsedToolInput if parsing fails
                }
              }

              const lastMessage = ensureAIMessage();

              const toolNamesFromRawString = rawToolString.split(';').map(name => name.trim()).filter(name => name.length > 0);

              if (toolNamesFromRawString.length > 0) {
                // Mark the entire group of tools in toolCallsMap (using the original toolId from rawData)
                toolCallsMap.value[toolId] = {
                  toolName: rawToolString, // Store the raw, potentially semicolon-separated string
                  status: 'start',
                  messageId: lastMessage.id, // Ensure message ID is associated
                };
              }

              for (const individualToolName of toolNamesFromRawString) {
                let effectiveToolName = individualToolName;

                if (individualToolName === "mcp_sse_call_tool" && parsedToolInput && parsedToolInput.mcp_sse_call_tool) {
                  if (parsedToolInput.mcp_sse_call_tool.tool_name) {
                    effectiveToolName = parsedToolInput.mcp_sse_call_tool.tool_name;
                    console.log(`[agent_thought] Extracted effectiveToolName: ${effectiveToolName} from mcp_sse_call_tool input for ${individualToolName}`);
                  } else {
                    console.warn(`[agent_thought] mcp_sse_call_tool input for ${individualToolName} did not contain expected tool_name property: `, parsedToolInput.mcp_sse_call_tool);
                  }
                }

                if (effectiveToolName) {
                  let chineseToolName;
                  if (effectiveToolName && effectiveToolName.startsWith('dataset_')) {
                    chineseToolName = '调用知识库';
                  } else {
                    chineseToolName = getToolDisplayName(effectiveToolName);
                  }
                  const toolCallText = '\n```calltool.start.' + toolId + '\n' + chineseToolName + '\n```'

                  // 检查是否已经存在相同ID的工具调用（任意状态）
                  const existingToolCallPattern = new RegExp(`\`\`\`calltool\\.(start|complete|error)\\.${toolId}\\s*\\n`, 'g');
                  const hasExistingToolCall = lastMessage.content &&
                    existingToolCallPattern.test(lastMessage.content);

                  if (!hasExistingToolCall) {
                    // 拼接工具调用信息到消息内容
                    lastMessage.content = (lastMessage.content || '') + toolCallText + '\n';
                    displayedContent.value = lastMessage.content; // 更新 displayedContent 以便后续 Markdown 解析使用
                    console.log(`[DEBUG] 添加新工具调用: "${chineseToolName}"`);
                  } else {
                    console.log(`[DEBUG] 已存在ID为 ${toolId} 的工具调用标记，跳过添加新的 start 标记。`);
                  }

                  console.log('更新工具content', lastMessage.content)
                  lastMessage.markdown = towxmlInstance(lastMessage.content, 'markdown', {
                    theme: 'light',
                    events: { /* 事件处理通常在渲染器中 */ }
                  });

                  if (!lastMessage.tool_calls || !Array.isArray(lastMessage.tool_calls)) {
                    lastMessage.tool_calls = [];
                  }

                  // 检查是否已经存在相同的工具调用（在视觉列表中）
                  const existingVisualToolEntry = lastMessage.tool_calls.find(
                    tc => tc.id === toolId
                  );

                  if (!existingVisualToolEntry) {
                    lastMessage.tool_calls.push({
                      name: chineseToolName,
                      status: 'start',
                      id: toolId, // All visual tools from this thought share the same parent toolId
                    });
                    console.log(`[agent_thought] Adding new visual tool entry: ${chineseToolName} (parent id: ${toolId})`);
                  } else {
                    console.log(`[agent_thought] Visual tool ${chineseToolName} already exists with id: ${toolId}.`);
                    existingVisualToolEntry.status = 'complete'
                    // 不要在这里更改状态，让observation处理状态变化
                  }
                }
              } // End for loop over toolNamesFromRawString

            } catch (err) {
              console.error("处理 agent_thought 事件出错:", err, rawData);
            }
          }
        }
      }
    } catch (e) {
      console.error("数据解析错误:", e, "原始数据:", rawStr);

      // 如果解析失败但有原始数据，尝试直接显示
      if (rawStr) {
        // 尝试从失败的数据中提取有用内容
        try {
          // 尝试匹配JSON对象或直接提取文本内容
          const jsonMatch = rawStr.match(/\{.*\}/s);
          const textContent = jsonMatch ? JSON.parse(jsonMatch[0])?.data?.outputs?.answer : null;

          if (textContent) {
            try {
              const lastMessage = ensureAIMessage();
              // 准备新内容
              const newContent = (lastMessage.content || '') + textContent;
              displayedContent.value = newContent;

              // 过滤 <think> 等标签
              const filteredContent = newContent
                .replace(/<think[\s\S]*?<\/think>/g, '')
                .replace(/<th[\s\S]*?<\/th/g, '')
                .replace(/<think[\s\S]*?<\/th/g, '')
                .replace(/<th[\s\S]*?<\/think>/g, '')
                .replace(/<(think|th)[\s\S]*$/g, '');

              try {
                // 尝试解析 Markdown 内容
                const parsedResult = towxmlInstance(filteredContent, 'markdown', {
                  theme: 'light',
                  events: { /* 事件处理通常在渲染器中 */ }
                });

                // 使用节流函数更新UI
                throttledUpdateUIMessage(lastMessage, parsedResult);
              } catch (error) {
                console.error('解析提取内容 Markdown 失败:', error);
                // 解析失败时使用原始文本
                throttledUpdateUIMessage(lastMessage, newContent);
              }
            } catch (err) {
              console.error("处理提取内容出错:", err);
            }
          } else {
            // 如果无法提取有用内容，显示错误
            try {
              const lastMessage = ensureAIMessage();
              if (!lastMessage.content) {
                const errorContent = CHAT_ERROR_MESSAGES.SERVER_BUSY;
                displayedContent.value = errorContent;
                lastMessage.content = errorContent;

                // 直接更新错误信息，尽量保持用户的滚动位置
                nextTick(() => {
                  scrollToBottom();
                });
              }
            } catch (err) {
              console.error("处理错误消息出错:", err);
            }
          }
        } catch (extractErr) {
          console.error("提取内容失败:", extractErr);
          // 如果进一步解析也失败，显示原始错误
          try {
            const lastMessage = ensureAIMessage();
            if (!lastMessage.content) {
              const errorContent = CHAT_ERROR_MESSAGES.SERVER_BUSY;
              displayedContent.value = errorContent;
              lastMessage.content = errorContent;

              // 直接更新错误信息
              nextTick(() => {
                scrollToBottom();
              });
            }
          } catch (err) {
            console.error("处理错误消息出错:", err);
          }
        }
      }
    }
  } catch (e) {
    console.error("数据处理异常:", e);

    // 处理通用错误
    try {
      const lastMessage = messages.value[messages.value.length - 1];
      if (lastMessage && lastMessage.type === 'ai') {
        // 使用常量中的标准错误消息
        const userFriendlyError = CHAT_ERROR_MESSAGES.SERVER_BUSY;

        // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
        if (lastMessage.content && lastMessage.content.trim()) {
          lastMessage.content = lastMessage.content + "\n" + userFriendlyError;
        } else {
          lastMessage.content = userFriendlyError;
        }

        displayedContent.value = lastMessage.content;
      }
    } catch (err) {
      console.error("处理通用错误出错:", err);
    }

    // 保存聊天记录
    saveHistoryMessages();

    displayedContent.value = '';
    lastMessageTimestamp.value = 0;
    // 清除请求任务引用
    currentRequestTask.value = null;
  }
};

// 发送消息
const handleSend = async (content = inputContent.value) => {
  console.log("🚀 ~ handleSend ~ content:", content)
  // 关闭任何打开的菜单
  isTitlePopupVisible.value = false;
  isMerchantSelectVisible.value = false;

  currentTaskId.value = null;
  currentRequestTask.value = null;

  // 确保content是字符串并且非空
  if (!content || (typeof content === 'string' && !content.trim())) return

  // 新增：检查输入长度并截断
  const maxLen = 1024;
  if (content.length > maxLen) {
    content = content.substring(0, maxLen);
    toast('超过文字输入上限，已为您自动截断');
    console.log('发送内容已截断至', maxLen, '字符');
  }

  // 检查登录状态
  if (!isLoggedIn.value) {
    uni.showModal({
      title: '请登录以继续',
      content: '登录后即可体验完整功能，享受个性化服务',
      confirmText: '立即登录',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          // 跳转到登录页面
          navigate('/pages/login/index')
        }
        // 取消则不做任何操作，自动关闭modal
      }
    })
    return
  }

  // 调试信息
  // console.clear();
  console.log("🚀 开始发送新消息 - " + new Date().toLocaleTimeString());
  console.log("📝 用户消息:", content);

  // 保存用户消息并添加到当前会话显示
  const userMessage = content

  // 添加用户消息到当前会话显示
  const messageId = addUserMessage(userMessage)

  // 将消息保存到历史记录存储中
  historyStore.addMessage({
    id: messageId, // 使用相同的ID避免重复
    role: 'user',
    type: 'user',
    content: userMessage,
    timestamp: Date.now()
  })

  // 更新消息相对时间
  handleMessageRelativeTime()

  // 新逻辑：无论如何都清空输入框，确保发送后输入框被清空
  inputContent.value = ''

  // 自动滚动到底部
  await nextTick()
  scrollToBottom()

  // 调用API
  setLoading(true)

  try {
    // 构建请求数据
    // 使用正确的Dify格式
    const requestData = {
      inputs: {},
      query: userMessage,
      response_mode: "streaming",  // 确保使用streaming模式
      user: userStore?.userInfo?.username || '',
      ...(currentConversationId.value ? { conversation_id: currentConversationId.value.includes(TEMP_ID) ? '' : currentConversationId.value } : {})
    }

    // 添加AI空消息，用于流式更新
    const aiMessageId = addAIMessage('');

    // 将消息保存到历史记录存储中
    historyStore.addMessage({
      id: aiMessageId,
      role: 'assistant',
      type: 'ai',
      content: '',
      markdown: '',
      timestamp: Date.now()
    })

    // 更新消息相对时间
    handleMessageRelativeTime()

    // 使用wx.request发起流式请求
    console.log("📡 发起API请求到:", `${getConfig().BASE_URL}/v1/chat-messages`);
    console.log("🔑 使用认证:", userStore.checkIsLoggedIn(), token, `Bearer ${token.value}`);

    // 清除任何可能存在的旧请求
    if (currentRequestTask.value) {
      console.log("⚠️ 存在未完成的请求，尝试中止");
      try {
        currentRequestTask.value.offChunkReceived() // 需传入与监听时同一个的函数对象
      } catch (e) {
        console.error("❌ 中止旧请求失败:", e);
      }
      currentRequestTask.value = null;
    }

    try {
      const header = {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream, application/json',  // 接受多种格式
      }

      // 仅当用户已登录时添加Authorization头
      if (userStore.checkIsLoggedIn() && token.value) {
        header['Authorization'] = `Bearer ${token.value}`
      }

      // 创建新请求
      requestTask = wx.request({
        timeout: 1000 * 60 * 10,
        url: `${getConfig().BASE_URL}/v1/chat-messages`,
        method: 'POST',
        header,
        data: requestData,
        enableChunked: true,
        responseType: "arraybuffer",
        complete() {
          setLoading(false)
          currentRequestTask.value = null

          // 更新AI消息的时间戳为当前时间
          const lastMessage = messages.value[messages.value.length - 1];
          if (lastMessage && lastMessage.type === 'ai') {
            // 更新消息时间戳为当前时间
            const currentTime = Date.now();
            lastMessage.timestamp = currentTime;

            // 同时更新历史记录中的时间戳
            if (historyStore.currentSession) {
              const aiMessage = historyStore.currentSession.messages.find(msg => msg.id === lastMessage.id && msg.type === 'ai');
              if (aiMessage) {
                aiMessage.timestamp = currentTime;
              }

              // 保存当前会话到历史记录
              historyStore.saveCurrentSession();
            }

            console.log('已更新AI消息时间戳为当前时间:', new Date(currentTime).toLocaleTimeString());
          }

          // 保存消息到本地存储
          saveHistoryMessages();

          // 更新所有消息的相对时间显示
          handleMessageRelativeTime()
        },
        success: (res) => {
          console.log("✅ 请求成功，状态码:", res.statusCode, "响应数据:", res.data);
          // 处理非200状态码且没有响应数据的情况
          if (res.statusCode !== 200 && (!res.data || res.data.length === 0)) {
            console.error('❗ 服务器返回非200状态码且没有响应数据:', res.statusCode);

            // 使用常量中的标准错误消息
            const errorMessage = CHAT_ERROR_MESSAGES.SERVER_BUSY;

            // 更新AI消息为错误消息
            const lastMessage = messages.value[messages.value.length - 1];
            if (lastMessage && lastMessage.type === 'ai') {
              // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
              if (lastMessage.content && lastMessage.content.trim()) {
                lastMessage.content = lastMessage.content + "\n" + errorMessage;
              } else {
                lastMessage.content = errorMessage;
              }

              // 创建新对象确保响应式更新
              const updatedMessage = {
                ...lastMessage,
                content: lastMessage.content
              };

              // 查找消息并替换
              const index = messages.value.findIndex(m => m.id === lastMessage.id);
              if (index !== -1) {
                messages.value.splice(index, 1, updatedMessage);
              }
            } else {
              addAIMessage(errorMessage);
            }

            setLoading(false);
            saveHistoryMessages();
            return;
          }
        },
        fail: (error) => {
          console.error('❌ API调用错误:', error)
          isRequestFail.value = true;
          // 如果是用户主动停止请求，则不处理错误
          if (error.errMsg && error.errMsg.includes('abort')) {
            console.log('🛑 请求已被用户中止，不显示错误信息');
            return;
          }
          if (error.errMsg && (
            error.errMsg.includes('ERR_INTERNET_DISCONNECTED') ||
            error.errMsg.includes('ERR_CONNECTION_ABORTED') ||
            error.errMsg.includes('ERR_INCOMPLETE_CHUNKED_ENCODING')
          )) {
            requestFailShowErrorText()
            isRequestFail.value = false
          } else {
            requestFailShowErrorText()
          }
        }
      });

      // 保存当前请求任务引用，并添加更多信息进行调试
      console.log("🔗 保存请求任务引用:", requestTask);
      currentRequestTask.value = requestTask;

      // 记录请求ID用于调试
      if (requestTask._requestId) {
        console.log("🆔 请求ID:", requestTask._requestId);
      }

      // 监听数据块接收
      if (requestTask?.onChunkReceived) {
        console.log("👂 设置数据块接收监听器");
        requestTask.onChunkReceived(handleChunkData);
      } else {
        throw new Error('当前环境不支持流式传输');
      }
    } catch (error) {
      console.error('❌ API调用错误:', error)

      // 更新AI消息为错误消息
      const lastMessage = messages.value[messages.value.length - 1];
      if (lastMessage && lastMessage.type === 'ai') {
        // 使用常量中的标准错误消息
        const errorMessage = CHAT_ERROR_MESSAGES.SERVER_BUSY;

        // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
        if (lastMessage.content && lastMessage.content.trim()) {
          lastMessage.content = lastMessage.content + "\n" + errorMessage;
        } else {
          lastMessage.content = errorMessage;
        }

        // 创建新对象确保响应式更新
        const updatedMessage = {
          ...lastMessage,
          content: lastMessage.content
        };

        // 查找消息并替换
        const index = messages.value.findIndex(m => m.id === lastMessage.id);
        if (index !== -1) {
          messages.value.splice(index, 1, updatedMessage);
        }
      } else {
        addAIMessage("服务器繁忙，请稍后重试。");
      }

      setLoading(false)
      saveHistoryMessages()
    }
  } catch (error) {
    console.error('API调用错误:', error)

    // 更新AI消息为错误消息
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage && lastMessage.type === 'ai') {
      // 使用常量中的标准错误消息
      const errorMessage = CHAT_ERROR_MESSAGES.SERVER_BUSY;

      // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
      if (lastMessage.content && lastMessage.content.trim()) {
        lastMessage.content = lastMessage.content + "\n" + errorMessage;
      } else {
        lastMessage.content = errorMessage;
      }

      // 创建新对象确保响应式更新
      const updatedMessage = {
        ...lastMessage,
        content: lastMessage.content
      };

      // 查找消息并替换
      const index = messages.value.findIndex(m => m.id === lastMessage.id);
      if (index !== -1) {
        messages.value.splice(index, 1, updatedMessage);
      }
    } else {
      addAIMessage("服务器繁忙，请稍后重试。");
    }

    setLoading(false)
    saveHistoryMessages()
  }
}

// 专门用于重发的发送函数，不会重复添加用户消息
const handleSendForRegenerate = async (content) => {
  console.log("🚀 ~ handleSendForRegenerate ~ content:", content)

  // 关闭任何打开的菜单
  isTitlePopupVisible.value = false;
  isMerchantSelectVisible.value = false;

  currentTaskId.value = null;
  currentRequestTask.value = null;

  // 确保content是字符串并且非空
  if (!content || (typeof content === 'string' && !content.trim())) return

  // 新增：检查输入长度并截断
  const maxLen = 1024;
  if (content.length > maxLen) {
    content = content.substring(0, maxLen);
    toast('超过文字输入上限，已为您自动截断');
    console.log('发送内容已截断至', maxLen, '字符');
  }

  // 检查登录状态
  if (!isLoggedIn.value) {
    uni.showModal({
      title: '请登录以继续',
      content: '登录后即可体验完整功能，享受个性化服务',
      confirmText: '立即登录',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          // 跳转到登录页面
          navigate('/pages/login/index')
        }
        // 取消则不做任何操作，自动关闭modal
      }
    })
    return
  }

  // 调试信息
  console.log("🚀 开始重发消息 - " + new Date().toLocaleTimeString());
  console.log("📝 重发消息内容:", content);

  // 重发时不添加用户消息，直接发送API请求
  const userMessage = content

  // 更新消息相对时间
  handleMessageRelativeTime()

  // 自动滚动到底部
  await nextTick()
  scrollToBottom()

  // 调用API
  setLoading(true)

  try {
    // 构建请求数据
    // 使用正确的Dify格式
    const requestData = {
      inputs: {},
      query: userMessage,
      response_mode: "streaming",  // 确保使用streaming模式
      user: userStore?.userInfo?.username || '',
      ...(currentConversationId.value ? { conversation_id: currentConversationId.value.includes(TEMP_ID) ? '' : currentConversationId.value } : {})
    }

    // 添加AI空消息，用于流式更新
    const aiMessageId = addAIMessage('');

    // 将AI消息保存到历史记录存储中
    historyStore.addMessage({
      id: aiMessageId,
      role: 'assistant',
      type: 'ai',
      content: '',
      markdown: '',
      timestamp: Date.now()
    })

    // 更新消息相对时间
    handleMessageRelativeTime()

    // 使用wx.request发起流式请求
    console.log("📡 发起重发API请求到:", `${getConfig().BASE_URL}/v1/chat-messages`);
    console.log("🔑 使用认证:", userStore.checkIsLoggedIn(), token, `Bearer ${token.value}`);

    // 清除任何可能存在的旧请求
    if (currentRequestTask.value) {
      console.log("⚠️ 存在未完成的请求，尝试中止");
      try {
        currentRequestTask.value.offChunkReceived() // 需传入与监听时同一个的函数对象
      } catch (e) {
        console.error("❌ 中止旧请求失败:", e);
      }
      currentRequestTask.value = null;
    }

    try {
      const header = {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream, application/json',  // 接受多种格式
      }

      // 仅当用户已登录时添加Authorization头
      if (userStore.checkIsLoggedIn() && token.value) {
        header['Authorization'] = `Bearer ${token.value}`
      }

      // 创建新请求
      requestTask = wx.request({
        timeout: 1000 * 60 * 10,
        url: `${getConfig().BASE_URL}/v1/chat-messages`,
        method: 'POST',
        header,
        data: requestData,
        enableChunked: true,
        responseType: "arraybuffer",
        complete() {
          setLoading(false)
          currentRequestTask.value = null

          // 更新AI消息的时间戳为当前时间
          const lastMessage = messages.value[messages.value.length - 1];
          if (lastMessage && lastMessage.type === 'ai') {
            // 更新消息时间戳为当前时间
            const currentTime = Date.now();
            lastMessage.timestamp = currentTime;

            // 同时更新历史记录中的时间戳
            if (historyStore.currentSession) {
              const aiMessage = historyStore.currentSession.messages.find(msg => msg.id === lastMessage.id && msg.type === 'ai');
              if (aiMessage) {
                aiMessage.timestamp = currentTime;
              }

              // 保存当前会话到历史记录
              historyStore.saveCurrentSession();
            }

            console.log('已更新AI消息时间戳为当前时间:', new Date(currentTime).toLocaleTimeString());
          }

          // 保存消息到本地存储
          saveHistoryMessages();

          // 更新所有消息的相对时间显示
          handleMessageRelativeTime()
        },
        success: (res) => {
          console.log("✅ 重发请求成功，状态码:", res.statusCode, "响应数据:", res.data);
          // 处理非200状态码且没有响应数据的情况
          if (res.statusCode !== 200 && (!res.data || res.data.length === 0)) {
            console.error('❗ 服务器返回非200状态码且没有响应数据:', res.statusCode);

            // 使用常量中的标准错误消息
            const errorMessage = CHAT_ERROR_MESSAGES.SERVER_BUSY;

            // 更新AI消息为错误消息
            const lastMessage = messages.value[messages.value.length - 1];
            if (lastMessage && lastMessage.type === 'ai') {
              // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
              if (lastMessage.content && lastMessage.content.trim()) {
                lastMessage.content = lastMessage.content + "\n" + errorMessage;
              } else {
                lastMessage.content = errorMessage;
              }

              // 创建新对象确保响应式更新
              const updatedMessage = {
                ...lastMessage,
                content: lastMessage.content
              };

              // 查找消息并替换
              const index = messages.value.findIndex(m => m.id === lastMessage.id);
              if (index !== -1) {
                messages.value.splice(index, 1, updatedMessage);
              }
            } else {
              addAIMessage(errorMessage);
            }

            setLoading(false);
            saveHistoryMessages();
            return;
          }
        },
        fail: (error) => {
          console.error('❌ 重发API调用错误:', error)
          isRequestFail.value = true;
          // 如果是用户主动停止请求，则不处理错误
          if (error.errMsg && error.errMsg.includes('abort')) {
            console.log('🛑 重发请求已被用户中止，不显示错误信息');
            return;
          }
          if (error.errMsg && (
            error.errMsg.includes('ERR_INTERNET_DISCONNECTED') ||
            error.errMsg.includes('ERR_CONNECTION_ABORTED') ||
            error.errMsg.includes('ERR_INCOMPLETE_CHUNKED_ENCODING')
          )) {
            requestFailShowErrorText()
            isRequestFail.value = false
          } else {
            requestFailShowErrorText()
          }
        }
      });

      // 保存当前请求任务引用，并添加更多信息进行调试
      console.log("🔗 保存重发请求任务引用:", requestTask);
      currentRequestTask.value = requestTask;

      // 记录请求ID用于调试
      if (requestTask._requestId) {
        console.log("🆔 重发请求ID:", requestTask._requestId);
      }

      // 监听数据块接收
      if (requestTask?.onChunkReceived) {
        console.log("👂 设置重发数据块接收监听器");
        requestTask.onChunkReceived(handleChunkData);
      } else {
        throw new Error('当前环境不支持流式传输');
      }
    } catch (error) {
      console.error('❌ 重发API调用错误:', error)

      // 更新AI消息为错误消息
      const lastMessage = messages.value[messages.value.length - 1];
      if (lastMessage && lastMessage.type === 'ai') {
        // 使用常量中的标准错误消息
        const errorMessage = CHAT_ERROR_MESSAGES.SERVER_BUSY;

        // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
        if (lastMessage.content && lastMessage.content.trim()) {
          lastMessage.content = lastMessage.content + "\n" + errorMessage;
        } else {
          lastMessage.content = errorMessage;
        }

        // 创建新对象确保响应式更新
        const updatedMessage = {
          ...lastMessage,
          content: lastMessage.content
        };

        // 查找消息并替换
        const index = messages.value.findIndex(m => m.id === lastMessage.id);
        if (index !== -1) {
          messages.value.splice(index, 1, updatedMessage);
        }
      } else {
        addAIMessage("服务器繁忙，请稍后重试。");
      }

      setLoading(false)
      saveHistoryMessages()
    }
  } catch (error) {
    console.error('重发API调用错误:', error)

    // 更新AI消息为错误消息
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage && lastMessage.type === 'ai') {
      // 使用常量中的标准错误消息
      const errorMessage = CHAT_ERROR_MESSAGES.SERVER_BUSY;

      // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
      if (lastMessage.content && lastMessage.content.trim()) {
        lastMessage.content = lastMessage.content + "\n" + errorMessage;
      } else {
        lastMessage.content = errorMessage;
      }

      // 创建新对象确保响应式更新
      const updatedMessage = {
        ...lastMessage,
        content: lastMessage.content
      };

      // 查找消息并替换
      const index = messages.value.findIndex(m => m.id === lastMessage.id);
      if (index !== -1) {
        messages.value.splice(index, 1, updatedMessage);
      }
    } else {
      addAIMessage("服务器繁忙，请稍后重试。");
    }

    setLoading(false)
    saveHistoryMessages()
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (userHasScrolled.value) {
    return;
  }
  nextTick(() => {
    // 方法1：通过scrollTop触发滚动
    getMessageBoxHeight().then((res) => {
      scrollTop.value = res;

      // 在滚动到底部后，如果已准备好则初始化观察器
      if (isReadyForObserver.value && messages.value.length > 0) {
        setupIntersectionObserver();
      }
    }).catch(error => console.log(error))
  })
}

// 处理新建会话
async function handleNewSession() {
  // 判断当前是否已经是新会话
  if (isSessionFromHistory.value === false && messages.value.length === 0) {
    toast('当前已是最新会话')
    return
  }

  // 清空当前会话消息
  clearMessages()
  // 在store中清空当前消息（确保UI更新）
  chatStore.messages = []
  // 重置当前会话ID
  currentConversationId.value = null

  // 标记当前会话为新建会话，滚动到顶部不加载更多消息
  isSessionFromHistory.value = false

  // 重置加载耗时统计
  loadingDuration.value = '';
  console.log('新建会话时重置加载耗时统计');

  // 清除并重新启动消息时间更新定时器
  if (messageTimeUpdateTimer) {
    clearInterval(messageTimeUpdateTimer)
    messageTimeUpdateTimer = null
  }
  messageTimeUpdateTimer = setInterval(() => {
    handleMessageRelativeTime()
  }, MESSAGE_TIME_UPDATE_INTERVAL)

  // 清空输入框
  inputContent.value = ''

  // 创建新的会话（在清空消息之后）
  historyStore.createNewSession()
  console.log("🚀 ~ handleNewSession ~ appStore.parameters:", appStore.parameters)

  try {
    // 主动获取最新parameters配置
    await appStore.fetchParameters()
    // 计算推荐问题区域高度
    nextTick(() => {
      calculateSuggestedQuestionsHeight();
    });

  } catch (error) {
    console.error('获取开场白配置失败:', error)
    // 即使获取失败也创建空会话
  }

}

// 打开推荐问题弹出层
const openRecommendedQuestionsPopup = () => {
  recommendedQuestionsRef.value.open('bottom')
}

let requestTask;

// 处理发送/停止按钮点击事件
const handleSendButtonClick = () => {
  if (isLoading.value) {
    stopCurrentRequest()
  } else {
    handleSend()
  }
}

// 使用节流包装发送/停止按钮点击事件，300毫秒内最多执行一次
const throttledHandleSendButtonClick = throttle(handleSendButtonClick, 300, {
  leading: true,
  trailing: false
})

// 处理遮罩触摸事件关闭菜单
const handleMaskTouch = () => {
  isTitlePopupVisible.value = false
  isMerchantSelectVisible.value = false
}

// 使用节流包装遮罩触摸事件，200毫秒内最多执行一次，防止误触
const throttledHandleMaskTouch = throttle(handleMaskTouch, 200, {
  leading: true,
  trailing: false
})

// 停止当前请求
const stopCurrentRequest = async () => {
  console.log('🛑 尝试停止当前请求', currentRequestTask.value);

  // 无论是否有 currentRequestTask.value，都尝试执行停止逻辑
  try {
    // 如果有task_id，调用停止API
    if (currentTaskId.value) {
      console.log('当前会任务ID 🛑 调用停止API，task_id:', currentTaskId.value);
      stopMessageIds.value.push(currentTaskId.value);

      await request(`/v1/chat-messages/${currentTaskId.value}/stop`, {
        method: 'POST',
        data: {
          user: userStore?.userInfo?.username || ''
        }
      });
      console.log('✅ 停止API调用成功');
    } else {
      console.log('⚠️ 没有task_id，不调用停止API');
    }
    // 尝试中止请求
    if (requestTask) {
      console.log('🛑 停止当前请求 ID:', requestTask._requestId || '未知');
      console.log('🛑 请求状态:', requestTask);
      if (typeof requestTask.abort === 'function') {
        requestTask.offChunkReceived(handleChunkData);
        requestTask.abort();
        console.log('✅ 成功调用abort方法');
      } else {
        console.warn('⚠️ 无法调用abort方法，尝试其他方法停止请求');
        if (wx.closeSocket) {
          wx.closeSocket();
          console.log('✅ 尝试关闭WebSocket连接');
        }
      }
    } else {
      console.log('⚠️ 没有活动的请求任务');
    }

    // 重置请求任务引用和task_id
    requestTask = null;
    currentTaskId.value = null;

    // 更新状态
    setLoading(false);

    displayedContent.value = '';
    lastMessageTimestamp.value = 0;
    console.log('stopMessageIds', stopMessageIds.value)

    // 添加提示消息
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage && lastMessage.type === 'ai') {
      // 如果消息内容为空，或者只有空白字符，则直接设置为"已暂停生成"
      if (!lastMessage.content || lastMessage.content.trim() === '') {
        lastMessage.content = CHAT_ERROR_MESSAGES.GENERATION_PAUSED;
      }

      // 创建新消息对象进行替换，确保响应式更新
      const updatedMessage = { ...lastMessage, content: lastMessage.content };

      // 更新工具调用状态为 stop
      if (updatedMessage.tool_calls && Array.isArray(updatedMessage.tool_calls)) {
        updatedMessage.tool_calls.forEach(toolCall => {
          if (toolCall.status === 'start') {
            toolCall.status = 'stop';

            // Update the text representation of the tool call
            if (typeof updatedMessage.content === 'string') {
              // Replace calltool.start.{id} with calltool.stop.{id} for this tool
              // 使用更灵活的正则表达式，允许工具名称前后有空格
              const startPattern = new RegExp(`\`\`\`calltool\\.start\\.(.*?)\\s*\\n\\s*${toolCall.name}\\s*\\n\\s*\`\`\``, 'g');
              const beforeContent = updatedMessage.content;

              // 使用函数作为replace的第二个参数，可以捕获正则表达式中的分组
              updatedMessage.content = updatedMessage.content.replace(startPattern, (_, toolId) => {
                return `\`\`\`calltool.stop.${toolId}\n${toolCall.name}\n\`\`\``;
              });


              updatedMessage.markdown = towxmlInstance(updatedMessage.content, 'markdown', {
                theme: 'light', // 根据需要设置主题
                events: { /* 事件处理通常在渲染器中 */ }
              });

              // 添加调试日志，检查替换是否成功
              if (beforeContent === updatedMessage.content) {
                console.log(`[DEBUG] 停止工具状态替换失败: 未找到匹配的工具调用文本 "${toolCall.name}"`);
                console.log(`[DEBUG] 正则表达式: ${startPattern}`);
                console.log(`[DEBUG] 内容片段: ${beforeContent.substring(0, 200)}...`);
              } else {
                console.log(`[DEBUG] 停止工具状态替换成功: "${toolCall.name}" 从 start 变为 stop`);
              }
            }
          }
        });
      }

      // 查找消息在数组中的索引
      const index = messages.value.findIndex(m => m.id === lastMessage.id);
      if (index !== -1) {
        // 替换整个对象以确保响应式更新
        messages.value.splice(index, 1, updatedMessage);
      }

      // 保存更改
      saveHistoryMessages();
    }
  } catch (error) {
    console.error('❌ 停止请求失败:', error);

    // 尝试强制重置状态
    currentRequestTask.value = null;
    currentTaskId.value = null;
    setLoading(false);

    // 如果停止失败，也给用户一个提示
    uni.showToast({
      title: '停止请求失败',
      icon: 'none',
      duration: 2000
    });
  }
}


// 打开文本编辑器弹窗
const openTextEditorPopup = () => {
  console.log("🚀 ~ openTextEditorPopup ~ textEditorPopupRef.value:", textEditorPopupRef.value)
  textEditorPopupRef.value?.open('bottom')
}

// 关闭文本编辑器弹窗
const closeTextEditorPopup = () => {
  textEditorPopupRef.value.close()
}

const scrollTop = ref(0)  // 当前滚动位置
const textareaHeight = ref(0);
// 控制scroll-into-view属性，用于平滑滚动到底部
const scrollIntoViewId = ref('');
// 判断是否显示展开按钮
const showExpandButton = computed(() => {
  const lineBreaks = (inputContent.value?.match(/\n/g) || []).length;
  return lineBreaks >= 4; // 5行文本包含4个换行符
});

// 推荐问题区域高度
const suggestedQuestionsHeight = ref(0);
// 推荐问题标题元素引用
const suggestedQuestionsTitleRef = ref(null);

// 计算推荐问题区域高度
const calculateSuggestedQuestionsHeight = () => {
  // 使用uni.createSelectorQuery获取推荐问题标题元素位置
  const query = uni.createSelectorQuery();
  query.select('.suggested-questions-title').boundingClientRect();
  query.select('.textarea').boundingClientRect();

  setTimeout(()=>{
    query.exec((res) => {
    if (!res || !res[0] || !res[1]) {
      console.log('无法获取推荐问题标题或输入区域的位置信息',res);
      // 使用默认高度
      suggestedQuestionsHeight.value = 'calc(100vh - 350rpx)';
      return;
    }

    const titleRect = res[0]; // 推荐问题标题的位置信息
    const inputRect = res[1]; // 输入区域的位置信息
    console.log("🚀 ~ query.exec ~ titleRect:", titleRect,inputRect)

    // 计算标题底部到输入区域顶部的距离
    const availableHeight = inputRect.top - (titleRect.bottom);
    console.log('推荐问题区域可用高度:', availableHeight);

    // 设置高度，留出一些边距
    suggestedQuestionsHeight.value = `${availableHeight}px`;
  });
  },300)
};

// 添加状态变量来跟踪文本域的聚焦状态
const isMainTextareaed = ref(false);
const isPopupTextareaed = ref(false);

// 聚焦时滚动到底部的函数
const scrollToBottomOn = () => {

  // 使用scroll-into-view属性实现平滑滚动到底部
  console.log('输入框聚焦，设置scroll-into-view属性滚动到底部');
  scrollIntoViewId.value = 'message-bottom';
};

// 主文本域的聚焦和失焦处理函数
const handleMainTextarea = () => {
  console.log('主输入框聚焦');
  isMainTextareaed.value = true;

  // 使用单独的函数处理聚焦时的滚动
  scrollToBottomOn();

  // 强制触发视图更新
  nextTick(() => {
    console.log('主输入框聚焦状态：', isMainTextareaed.value);
  });
};

const handleMainTextareaBlur = () => {
  console.log('主输入框失焦');
  isMainTextareaed.value = false;

  // 强制触发视图更新
  nextTick(() => {
    console.log('主输入框失焦状态：', isMainTextareaed.value);
  });
};

// 弹窗文本域的聚焦和失焦处理函数
const handlePopupTextarea = () => {
  console.log('弹窗输入框聚焦');
  isPopupTextareaed.value = true;

  // 强制触发视图更新
  nextTick(() => {
    console.log('弹窗输入框聚焦状态：', isPopupTextareaed.value);
  });
};

const handlePopupTextareaBlur = () => {
  console.log('弹窗输入框失焦');
  isPopupTextareaed.value = false;

  // 强制触发视图更新
  nextTick(() => {
    console.log('弹窗输入框失焦状态：', isPopupTextareaed.value);
  });
};

// 处理textarea输入事件
const handleTextareaInput = () => {
  // 小程序环境下获取元素高度
  const query = uni.createSelectorQuery()
  query.select('.textarea').boundingClientRect(res => {
    if (res) {
      console.log('Textarea height:', res.height)
      // 如果高度发生变化，重新计算推荐问题区域高度
      if (textareaHeight.value !== res.height) {
        textareaHeight.value = res.height;
        // 重新计算推荐问题区域高度
        nextTick(() => {
          calculateSuggestedQuestionsHeight();
        });
      } else {
        textareaHeight.value = res.height;
      }
    }
  }).exec()
}

// 获取消息列表高度
const getMessageBoxHeight = () => {
  return new Promise((resolve, reject) => {
    // 使用uni.createSelectorQuery创建查询
    const query = uni.createSelectorQuery()

    // 选择.message-box元素
    query.select('.message-box').boundingClientRect(data => {
      if (data) {
        // 返回元素高度
        resolve(data.height)
      } else {
        // 如果未找到元素，返回错误
        reject(new Error('未找到.message-box元素'))
      }
    }).exec()
  })
}

// 滚动可见性相关变量
const intersectionObserver = ref(null)
const visibleMessageIds = ref(new Set())
const isReadyForObserver = ref(false) // 标记是否准备好初始化观察器

// 消息可见性变化处理函数
const handleMessageVisibility = (messageId, isVisible) => {
  if (isVisible) {
    // 当消息可见时执行空函数
    onMessageVisible(messageId)
    // 添加到可见消息集合
    visibleMessageIds.value.add(messageId)
  } else {
    // 从可见消息集合中移除
    visibleMessageIds.value.delete(messageId)
  }
}

// 当消息可见时执行的空函数
const onMessageVisible = (messageId) => {
  // 查找对应的消息
  const message = messages.value.find(m => m.id === messageId && m.type === 'ai');
  if (!message) {
    console.log('消息不存在:', messageId);
    return;
  }

  console.log('消息可见:', messageId, '类型:', message.type);

  // 仅处理AI消息
  if (message.type === 'ai') {
    // 检查是否已经处理过
    if (!message.markdown && message.content) {
      try {
        // 解析Markdown内容
        console.log('处理AI消息的Markdown:', messageId);

        // 过滤内容中的特殊标签
        const filteredContent = message.content
          .replace(/<think[\s\S]*?<\/think>/g, '')
          .replace(/<th[\s\S]*?<\/th/g, '')
          .replace(/<think[\s\S]*?<\/th/g, '')
          .replace(/<th[\s\S]*?<\/think>/g, '')
          .replace(/<(think|th)[\s\S]*$/g, '');

        // 解析Markdown
        const parsedMarkdown = towxmlInstance(filteredContent, 'markdown', {
          theme: 'light',
          events: { /* 事件处理通常在渲染器中 */ }
        });

        // 更新消息对象
        const index = messages.value.findIndex(m => m.id === messageId && m.type === 'ai');
        if (index !== -1) {
          // 创建新对象以确保响应式更新
          const updatedMessage = {
            ...message,
            content: filteredContent,
            markdown: parsedMarkdown
          };

          // 替换原消息
          messages.value.splice(index, 1, updatedMessage);
          // console.log('已更新消息的Markdown:', messageId,parsedMarkdown);

          // 检查当前消息是否是最后一条消息
          if (index === messages.value.length - 1) {
            console.log('处理的是最后一条消息，滚动到底部');
            // 使用nextTick确保DOM更新后再滚动
            nextTick(() => {
              scrollToBottom();
            });
          }
        }
      } catch (error) {
        console.error('处理Markdown失败:', error);
      }
    }
  }
}

// 初始化IntersectionObserver
const setupIntersectionObserver = () => {
  return;
  // 如果尚未准备好或没有消息，则不初始化
  if (!isReadyForObserver.value || !messages.value.length) {
    console.log('尚未准备好初始化IntersectionObserver或没有消息')
    return
  }

  // 确保在小程序环境下运行
  if (typeof wx !== 'undefined' && wx.createIntersectionObserver) {
    // 如果已有观察器，先销毁
    if (intersectionObserver.value) {
      intersectionObserver.value.disconnect()
      intersectionObserver.value = null
      console.log('已销毁旧的IntersectionObserver')
    }

    // 延迟创建观察器，确保DOM已经渲染完成
    setTimeout(() => {
      try {
        // 获取当前页面实例
        const page = getCurrentPages()[getCurrentPages().length - 1]

        // 创建IntersectionObserver实例
        intersectionObserver.value = wx.createIntersectionObserver(page, {
          observeAll: true         // 同时观测多个目标节点
        })

        console.log('创建IntersectionObserver成功')

        // 设置相对于视口的参照区域
        intersectionObserver.value.relativeToViewport({top:1})

        // 观察所有消息气泡元素
        intersectionObserver.value.observe('.message-bubble-ai', (res) => {
          console.log("观察到元素相交:", res)

          // 获取当前观察到的元素dataset
          const dataset = res.dataset
          if (dataset && dataset.messageId) {
            // 使用intersectionRatio判断元素是否可见
            handleMessageVisibility(dataset.messageId, res.intersectionRatio > 0)
          }
        })

        console.log('IntersectionObserver设置完成')
      } catch (error) {
        console.error('初始化IntersectionObserver失败:', error)
      }
    }, 500)
  } else {
    console.warn('当前环境不支持IntersectionObserver')
  }
}

onReady(() => {
  // 获取胶囊按钮位置信息
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
  if (menuButtonInfo) {
    navbarHeight.value = menuButtonInfo.bottom + 10;
    console.log('Navbar height (胶囊底部+10px):', navbarHeight.value);
  } else {
    console.warn('无法获取胶囊按钮信息，使用默认高度');
    navbarHeight.value = 50;
  }

  // 初始化消息时间更新定时器 - 每30秒更新一次
  if (messageTimeUpdateTimer) {
    clearInterval(messageTimeUpdateTimer)
    messageTimeUpdateTimer = null
  }
  messageTimeUpdateTimer = setInterval(() => {
    handleMessageRelativeTime()
  }, MESSAGE_TIME_UPDATE_INTERVAL)
  console.log('已启动消息时间更新定时器')

  // 清除加载耗时定时器（如果存在）
  if (loadingDurationTimer) {
    clearInterval(loadingDurationTimer)
    loadingDurationTimer = null
  }

  // 计算推荐问题区域高度
  nextTick(() => {
    calculateSuggestedQuestionsHeight();
  });

  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const { sessionId } = currentPage.$page?.options || {}
  console.log("🚀 ~ onReady ~ sessionId:", sessionId)
  nextTick(() => {
    handleTextareaInput();
  });
  if (sessionId) {
    // 如果有会话ID，加载对应的历史会话
    try {
      const storage = uni.getStorageSync('chat_history')
      if (storage) {
        const targetSession = storage.find(session => session.sessionId === sessionId)
        if (targetSession) {
          // 设置当前会话
          historyStore.currentSession = targetSession
          // 清空当前消息列表
          clearMessages()
          // 添加历史消息到当前会话
          targetSession.messages.forEach(message => {
            if (message.type === 'user') {
              // 使用消息的ID添加用户消息，避免重复
              addUserMessage(message.content, message.id)
            } else if (message.type === 'ai') {
              // 使用消息的ID添加AI消息，避免重复
              addAIMessage(message.content, message.id)
            }
          })
          // 不需要再次保存到历史记录，因为消息已经存在于历史记录中
          // saveHistoryMessages()
        }
      }
    } catch (error) {
      console.error('加载历史会话失败:', error)
    }
  } else {
    // 更新当前会话ID
    if (historyStore.currentSession?.sessionId) {
      currentConversationId.value = historyStore.currentSession.sessionId
      console.log("🚀 从历史记录加载当前会话ID:", currentConversationId.value)
    }
  }

  scrollToBottom()

  // 初始化变量，但不立即设置观察器
  isReadyForObserver.value = false;
})

// 页面隐藏时停止定时器
onHide(() => {
  console.log('页面隐藏，停止消息时间更新定时器')
  if (messageTimeUpdateTimer) {
    clearInterval(messageTimeUpdateTimer)
    messageTimeUpdateTimer = null
    console.log('已停止消息时间更新定时器')
  }
})


const requestFailShowErrorText = (errText = '') => {
  // 使用常量中的标准错误消息
  const errorMessage = errText || CHAT_ERROR_MESSAGES.SERVER_BUSY;

  // 更新AI消息为错误消息
  const lastMessage = messages.value[messages.value.length - 1];
  if (lastMessage && lastMessage.type === 'ai') {
    // 如果消息已有内容，则在最后添加错误信息，否则直接设置错误信息
    if (lastMessage.content && lastMessage.content.trim()) {
      lastMessage.content = lastMessage.content + "\n" + errorMessage;
    } else {
      lastMessage.content = errorMessage;
    }

    // 创建新对象确保响应式更新
    const updatedMessage = {
      ...lastMessage,
      content: lastMessage.content
    };

    // 查找消息并替换
    const index = messages.value.findIndex(m => m.id === lastMessage.id);
    if (index !== -1) {
      messages.value.splice(index, 1, updatedMessage);
    }
  } else {
    addAIMessage(errorMessage);
  }

  setLoading(false)
  saveHistoryMessages()
  scrollToBottom()

}

// 页面显示时重新加载会话
onShow(async () => {
  console.log('页面显示，开始加载会话...')

  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1]
  const { sessionId } = currentPage.$page?.options || {}
  console.log('页面参数 sessionId:', sessionId)

  // 检查用户登录状态
  console.log('用户登录状态:', isLoggedIn.value, 'token:', token.value)

  // 如果未登录，则调用 handleNewSession 重置会话
  if (!userStore.checkIsLoggedIn()) {
    console.log('用户未登录，调用 handleNewSession 重置会话');
    // 增加条件：如果当前不是新会话，才执行 handleNewSession
    if (!(isSessionFromHistory.value === false && messages.value.length === 0)) {
      handleNewSession();
    } else {
      console.log('用户未登录，但当前已经是新会话，跳过 handleNewSession');
    }
  }

  if (sessionId) {
    console.log('加载指定会话:', sessionId)
    // 标记当前会话为从会话列表进入的，滚动到顶部可以加载更多消息
    isSessionFromHistory.value = true;
    console.log('当前会话标记为从历史进入，滚动到顶部将加载更多消息');

    // 如果有会话ID，加载对应的历史会话
    try {
      const storage = uni.getStorageSync('chat_history')
      console.log('本地存储数据:', storage)

      if (storage) {
        const targetSession = storage.find(session => session.sessionId === sessionId)
        console.log('找到的目标会话:', targetSession)

        if (targetSession) {
          // 设置当前会话
          historyStore.currentSession = targetSession
          // 清空当前消息列表
          clearMessages()
          // 添加历史消息到当前会话
          targetSession.messages.forEach(message => {
            if (message.type === 'user') {
              // 使用消息的ID添加用户消息，避免重复
              addUserMessage(message.content, message.id)
            } else if (message.type === 'ai') {
              // 使用消息的ID添加AI消息，避免重复
              addAIMessage(message.content, message.id)
            }
          })
          // 不需要再次保存到历史记录，因为消息已经存在于历史记录中
          // saveHistoryMessages()
          console.log('成功加载指定会话')
        } else {
          console.warn('未找到匹配的会话')
        }
      } else {
        console.warn('本地存储中没有会话数据')
      }
    } catch (error) {
      console.error('加载历史会话失败:', error)
      uni.showToast({
        title: '加载会话失败',
        icon: 'none'
      })
    }
  } else {
    console.log('没有指定会话ID，尝试从API加载')
    // 标记当前会话为新建会话，滚动到顶部不加载更多消息
    isSessionFromHistory.value = false;
    console.log('当前会话标记为新建会话，滚动到顶部不加载更多消息');

    // 更新当前会话ID
    if (historyStore.currentSession?.sessionId) {
      currentConversationId.value = historyStore.currentSession.sessionId
      console.log("🚀 从历史记录加载当前会话ID:", currentConversationId.value)

      // 重置分页状态 - messagesHasMore由store管理
      isLoadingMoreMessages.value = false;

      // 如果有消息，设置第一条消息的ID作为分页标记
      if (messages.value.length > 0) {
        firstMessageId.value = messages.value[0].id;
        console.log('设置分页标记 firstMessageId:', firstMessageId.value, '来自消息:', messages.value[0]);
      } else {
        firstMessageId.value = null;
        console.log('消息列表为空，重置 firstMessageId 为 null');
      }
    }

    // 只有登录用户才从API加载
    if (userStore.checkIsLoggedIn()) {
      try {
        // 主动获取最新parameters配置
        appStore.fetchParameters().then(()=>{
          nextTick(()=>calculateSuggestedQuestionsHeight())
        })
        console.log('开始从API加载会话列表...')
        await historyStore.loadConversations()
        console.log('会话列表加载完成:', historyStore.sessions)

      } catch (error) {
        console.error('加载会话列表失败:', error)
        uni.showToast({
          title: '加载会话列表失败',
          icon: 'none'
        })
      }
    }
  }

  if (isRequestFail.value) {
    console.log('页面关闭请求失败，',isRequestFail.value,currentConversationId.value)
    const lastMessage = messages.value[messages.value.length - 1];
    if(lastMessage.type === 'ai' && lastMessage.content === ''){
      requestFailShowErrorText()
    }
    if (currentConversationId.value && !currentConversationId.value.includes(TEMP_ID)) {
      await historyStore.fetchMessagesByConversationId(
        currentConversationId.value,
        null,
        true // 表示这是加载更多消息，应该追加到现有消息中
      )
    }
    isRequestFail.value = false;
  }


  scrollToBottom()

  // 重新启动消息时间更新定时器
  if (!messageTimeUpdateTimer) {
    messageTimeUpdateTimer = setInterval(() => {
      handleMessageRelativeTime()
    }, MESSAGE_TIME_UPDATE_INTERVAL)
    console.log('页面显示，重新启动消息时间更新定时器')
  }

  // 重新计算推荐问题区域高度
  nextTick(() => {
    calculateSuggestedQuestionsHeight();
  });

  // 如果已有消息，且不是从会话列表进入的新会话，则准备好观察器
  if (messages.value.length > 0 && !isSessionFromHistory.value) {
    isReadyForObserver.value = true;
    nextTick(() => {
      setupIntersectionObserver();
    });
  }
})

// 组件卸载时清除定时器
onUnload(() => {
  // 取消节流函数
  throttledUpdateUIMessage.cancel()
  throttledHandleSendButtonClick.cancel()
  throttledHandleMaskTouch.cancel()
  handleRegenerate.cancel()

  // 清除消息时间更新定时器
  if (messageTimeUpdateTimer) {
    clearInterval(messageTimeUpdateTimer)
    messageTimeUpdateTimer = null
    console.log('已清除消息时间更新定时器')
  }

  // 清除加载耗时定时器
  if (loadingDurationTimer) {
    clearInterval(loadingDurationTimer)
    loadingDurationTimer = null
    console.log('已清除加载耗时定时器')
  }

  // 清理IntersectionObserver
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect()
    intersectionObserver.value = null
    console.log('已清理IntersectionObserver')
  }
})

// 打开更多选项抽屉
const openMoreOptions = () => {
  // 关闭任何打开的菜单
  isTitlePopupVisible.value = false;
  isMerchantSelectVisible.value = false;

  moreOptionsRef.value.open('left')
}

// 处理选择历史会话
const handleSelectSession = async (session) => {
  if(session.sessionId === historyStore.currentSession?.sessionId){
    return;
  }

  // 重置观察器状态
  isReadyForObserver.value = false;
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
    intersectionObserver.value = null;
  }

  try {
    // 确保session和messages存在
    if (!session || !Array.isArray(session.messages)) {
      console.error('无效的会话数据:', session)
      return
    }

    // 更新historyStore的当前会话
    historyStore.currentSession = session
    console.log("🚀 ~ handleSelectSession ~ session:", session)
    // 保存当前会话ID
    if (session.sessionId) {
      currentConversationId.value = session.sessionId;
      console.log("🚀 从历史会话加载保存当前会话ID:", currentConversationId.value);

      // 标记当前会话为从会话列表进入的，滚动到顶部可以加载更多消息
      isSessionFromHistory.value = true;

      isLoadingMoreMessages.value = false;

      // 先重置 firstMessageId
      firstMessageId.value = null;

      // 重置用户滚动标识
      userHasScrolled.value = false;

      // 重置加载耗时统计
      loadingDuration.value = '';

      // 显示加载指示器
      isLoadingMoreMessages.value = true;

      try {
        // 调用API获取会话消息
        const result = await historyStore.fetchMessagesByConversationId(session.sessionId);
        console.log("🚀 ~ handleSelectSession ~ 加载会话消息结果:", result);

        // 设置分页标记
        if (result.messages && result.messages.length > 0) {
          firstMessageId.value = result.first_id;
          console.log('选择会话后设置 firstMessageId:', firstMessageId.value);
        }

        // 标记为准备好初始化观察器
        isReadyForObserver.value = true;
        console.log('消息加载完成，已准备好初始化观察器');

        // 滚动到底部，滚动完成后会初始化观察器
        nextTick(() => {
          scrollToBottom();
        });
      } catch (error) {
        console.error('加载会话消息失败:', error);
        toast('获取消息失败，请稍后重试', { icon: 'none' });
      } finally {
        isLoadingMoreMessages.value = false;
      }
    }

    // 关闭抽屉
    moreOptionsRef.value.close()
    handleMessageRelativeTime()
  } catch (error) {
    console.error('处理历史消息失败:', error)
    uni.showToast({
      title: '加载消息失败',
      icon: 'none'
    })
  }
}

// 处理点赞/点踩反馈
// 处理推荐问题点击
const handleSuggestedQuestion = (question) => {
  // 将问题作为用户消息发送
  // inputContent.value = question
  handleSend(question)

  // 关闭推荐问题弹出层
  if (recommendedQuestionsRef.value) {
    recommendedQuestionsRef.value.close()
  }
}

const handleFeedback = (rating, message) => {
  const { id } = message;
  console.log(`用户反馈了消息:`, id)
  request(`/v1/messages/${id}/feedbacks`, {
    method: 'POST',
    data: {
      rating,
      content: rating
    }
  }).then(res => {
    message.feedback = { rating }
    console.log('反馈提交成功:', res)
  }).catch(err => {
    console.error('反馈提交失败:', err)
  })
}

// 处理重新生成消息
const handleRegenerateInternal = (message) => {
  console.log('重新生成消息:', message.id)

  // 如果正在加载中，不允许重新生成
  if (isLoading.value) {
    toast('对话进行中，请稍后…')
    return
  }

  // 找到当前消息在消息数组中的索引
  const currentMessageIndex = messages.value.findIndex(m => m.id === message.id && m.type === 'ai');
  console.log("🚀 ~ handleRegenerate ~ currentMessageIndex:", messages.value)

  if (currentMessageIndex === -1) {
    toast('消息不存在')
    return
  }

  // 向前查找上一条用户消息
  let previousUserMessage = null;
  for (let i = currentMessageIndex - 1; i >= 0; i--) {
    if (messages.value[i].type === 'user') {
      previousUserMessage = messages.value[i];
      break;
    }
  }

  if (!previousUserMessage || !previousUserMessage.content) {
    toast('没有找到可重新生成的消息')
    return
  }

  // 重置用户滚动标记，确保滚动到底部
  userHasScrolled.value = false;

  // 重发时不应该重复添加用户消息，而是直接发送API请求
  // 调用修改后的handleSend函数，传入第二个参数表示这是重发操作
  handleSendForRegenerate(previousUserMessage.content)

  // 强制滚动到底部
  nextTick(() => {
    scrollToBottom();
  })
}

// 使用节流包装重新生成消息函数，300毫秒内最多执行一次，防止重复点击
const handleRegenerate = throttle(handleRegenerateInternal, 300, {
  leading: true,
  trailing: false
})

// 判断当前会话是否是从会话列表进入的
const isSessionFromHistory = ref(false);
const userHasScrolled = ref(false);

// 处理滚动事件
const handleScroll = (e) => {

  // 清除scroll-into-view属性，避免干扰用户的滚动
  if (scrollIntoViewId.value) {
    scrollIntoViewId.value = '';
    console.log('清除scroll-into-view属性');
  }

  // 获取滚动信息
  const currentScrollTop = e.detail.scrollTop; // 当前滚动位置

  // 使用uni.createSelectorQuery获取滚动区域和内容的实际高度
  const query = uni.createSelectorQuery();
  query.select('.message-list').boundingClientRect();
  query.select('.message-box').boundingClientRect();

  query.exec((res) => {
    if (!res || !res[0] || !res[1]) {
      console.log('无法获取滚动区域或内容的高度信息');
      return;
    }

    const scrollViewRect = res[0]; // 滚动区域的尺寸信息
    const contentRect = res[1];    // 内容的尺寸信息

    // 计算距离底部的距离
    const distanceFromBottom = contentRect.height - currentScrollTop - scrollViewRect.height;

    // 如果距离底部小于一定值，认为用户在底部
    const isAtBottom = distanceFromBottom < 20; // 允许20像素的误差

    // 如果不在底部，标记用户已手动滚动
    if (!isAtBottom) {
      if (!userHasScrolled.value) {
        userHasScrolled.value = true;
        console.log('用户已手动滚动离开底部，停止自动滚动');
      }
    } else {
      // 如果在底部，重置手动滚动标记
      if (userHasScrolled.value) {
        userHasScrolled.value = false;
        console.log('用户已滚动到底部，恢复自动滚动');
      }
    }
  });
};

// 处理滚动到顶部加载更多消息
const handleScrollToUpper = async () => {
  // 如果没有更多消息或正在加载中，则不处理
  if (!messagesHasMore.value || isLoadingMoreMessages.value || !currentConversationId.value) {
    console.log('滚动到顶部，但不加载更多消息:', {
      messagesHasMore: messagesHasMore.value,
      isLoadingMoreMessages: isLoadingMoreMessages.value,
      currentConversationId: currentConversationId.value
    })
    return
  }

  // 如果当前会话不是从会话列表进入的（即新建的会话），则不执行分页加载
  if (!isSessionFromHistory.value) {
    console.log('当前是新建会话，滚动到顶部不加载更多消息')
    return
  }

  // 检查firstMessageId是否正确设置
  console.log('滚动到顶部，开始加载更多消息:', {
    firstMessageId: firstMessageId.value,
    messagesLength: messages.value.length,
    firstMessage: messages.value.length > 0 ? messages.value[0] : null
  })

  // 显示加载提示
  isLoadingMoreMessages.value = true

  const firstAiMessage = messages.value?.find(item => item.type === 'ai' && typeof item.id === 'string')
  // 确保 firstMessageId 已设置
  if (firstAiMessage) {
    console.log('检测到 firstMessageId 未设置，但消息列表不为空，自动设置为第一条消息的ID', messages.value);
    firstMessageId.value = firstAiMessage.id;
  }

  // 再次检查 firstMessageId
  if (!firstMessageId.value) {
    console.log('警告: firstMessageId 仍然为空，无法加载更多消息');
    isLoadingMoreMessages.value = false;
    return;
  }

  try {
    // 调用API获取更多消息
    console.log('开始调用API获取更多消息，参数:', {
      conversationId: currentConversationId.value,
      firstId: firstMessageId.value
    });

    const result = await historyStore.fetchMessagesByConversationId(
      currentConversationId.value,
      firstMessageId.value,
      true // 表示这是加载更多消息，应该追加到现有消息中
    )

    console.log('加载更多消息结果:', result)

    // 更新状态 - 不需要手动设置messagesHasMore，因为已经在store中更新
    console.log('加载更多消息后的结果:', {
      has_more: result.has_more,
      first_id: result.first_id,
      messagesCount: result.messages.length,
      messagesHasMore: messagesHasMore.value
    })

    if (result.messages.length > 0) {
      firstMessageId.value = result.first_id
      console.log('更新 firstMessageId:', firstMessageId.value)
    }
  } catch (error) {
    console.error('加载更多消息失败:', error)
    uni.showToast({
      title: '加载消息失败',
      icon: 'none'
    })
  } finally {
    isLoadingMoreMessages.value = false
  }
}

// 处理导航栏按钮点击
const currentConversationName = computed(() => {
  // 获取会话名称，如果不存在则使用默认值
  const name = historyStore?.currentSession?.name ?? '新会话';
  // 移除所有换行符并限制长度
  return name.replace(/\n/g, '').trim();
})

// 处理重新编辑消息
const handleEditMessage = (message) => {
  if (message && message.content) {
    inputContent.value = message.content;
  }
}

// 定义工具名称映射
const toolNameMap = {
  getStidStname: '调用权限校验工具',
  get_current_time: '调用时间查询工具',
  mcp_sse_list_tools: '获取可用工具',
  Rewrite_Agent: '调用喂车意图识别智能体',
  DataQueryAgent: '调用喂车数据查询智能体',
  DataAnalysis_Agent: '调用喂车数据分析智能体',
  MarketingAgent: '调用喂车方案设计智能体',
};

// 获取工具显示名称
const getToolDisplayName = (toolName) => {
  // 优先使用appStore.parameters.tools_map中的映射
  console.log('getToolDisplayName toolName', appStore.parameters?.tools_map?.[toolName])
  if (appStore.parameters?.tools_map?.[toolName]) {
    return appStore.parameters.tools_map[toolName];
  }
  // 如果tools_map中找不到，则使用toolNameMap
  return toolNameMap[toolName] || toolName;
};

</script>

<template>
  <!-- 添加viewport meta标签确保安全区域正确处理 -->
  <page-meta>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
  </page-meta>
  <wd-navbar :fixed="true" :placeholder="true" custom-style="background:#f5f5f5;" :bordered="false" :zIndex="90" safeAreaInsetTop
    @click-left="openMoreOptions">
    <template #left>
      <view>
        <view class="i-line-md-close-to-menu-alt-transition text-40" v-if="userStore.checkIsLoggedIn()">
        </view>
      </view>
    </template>
    <template #title>
      <!-- 设置 position: relative; 以便绝对定位的子元素相对其定位 -->
      <view class="flex flex-col items-center relative w-full">
        <!-- 标题和下拉图标，点击切换标签显示 -->
        <view class="flex items-center justify-center" :class="{ 'cursor-pointer': userStore.checkIsLoggedIn() }"
          @click.stop="userStore.checkIsLoggedIn() && toggleTitlePopup()">
          <text text="32 #232323" class="max-w-260 truncate">{{ currentConversationName }}</text>
          <view v-if="userStore.checkIsLoggedIn()" class="ml-1">
            <view :class="['i-ic-baseline-arrow-drop-down text-50 transition-all duration-300 ease-in-out',
              isTitlePopupVisible ? 'rotate-180' : '']">
            </view>
          </view>
        </view>
      </view>
    </template>
  </wd-navbar>

  <view class="chat-container flex flex-col relative bg-#f5f5f5">
    <!-- 触摸遮罩关闭菜单 -->
    <view v-if="isTitlePopupVisible || isMerchantSelectVisible" class="fixed inset-0 bg-transparent z-95"
      @touchstart="throttledHandleMaskTouch">
    </view>

    <!-- 条件渲染的标签 - 弹出菜单选项 -->
    <view v-if="isTitlePopupVisible"
      class="absolute top-0 left-0 w-screen box-border px-0 py-0 bg-white shadow-md z-100 rd-b-lg" @click.stop>
      <view v-for="(option, index) in titleMenuOptions" :key="option.id" class="relative">
        <!-- 选项项 -->
        <view class="flex items-center justify-between px-4 py-3" @click="handleMenuItemClick(option)">
          <view class="flex items-center">
            <text :class="option.icon + ' mr-2 text-32 text-gray-700'"></text>
            <!-- 如果是商户选项且有当前商户，显示当前商户名称 -->
            <text v-if="option.id === 'merchant' && currentMerchantObject"
              class="text-30 text-gray-800 max-w-60vw truncate">
              {{ currentMerchantObject.text || '未选择商户' }}
            </text>
            <!-- 否则显示普通标签 -->
            <text v-else class="text-30 text-gray-800">{{ option.label }}</text>
          </view>
          <!-- 商户选项右侧显示"切换商户"文本 -->
          <text v-if="option.id === 'merchant'" class="text-28 text-primary ml-2">切换商户</text>
          <wd-switch size="40rpx" v-if="option.showSwitch" :model-value="userStore.voicePlayEnabled.value" @change="(val) => {
            console.log('Switch value:', val);
            userStore.setVoicePlayEnabled(val)
          }" class="ml-auto" />
        </view>
        <!-- 分隔线 (除了最后一项) -->
        <view v-if="index < titleMenuOptions.length - 1" class="mx-4 h-[1px] bg-gray-200"></view>
      </view>
    </view>

    <!-- 商户选择面板 -->
    <view v-if="isMerchantSelectVisible"
      class="absolute top-0 left-0 w-screen box-border px-0 py-0 bg-white shadow-md z-100 rd-b-lg" @click.stop>
      <view class="flex items-center justify-between px-4 py-3 border-b border-gray-200">
        <text class="text-32 font-medium text-gray-800">选择商户</text>
        <text class="i-ic-round-close text-32 text-gray-600" @click="isMerchantSelectVisible = false"></text>
      </view>
      <view class="max-h-60vh overflow-auto">
        <view v-for="(merchant, index) in formattedMerchants" :key="merchant.value" class="relative">
          <view class="flex items-center justify-between px-4 py-3 hover:bg-gray-100" hover-class="bg-gray-50"
            @click="handleMerchantChange(merchant.value); isMerchantSelectVisible = false;">
            <text class="text-30 text-gray-800">{{ merchant.text }}</text>
            <text v-if="merchant.value === currentMerchantId" class="i-ic-round-check text-32 text-primary"></text>
          </view>
          <!-- 分隔线 (除了最后一项) -->
          <view v-if="index < formattedMerchants.length - 1" class="mx-4 h-[1px] bg-gray-200"></view>
        </view>
      </view>
    </view>

    <view v-if="!userStore.checkIsLoggedIn()" class="bg-white px-4 py-2 flex items-center justify-between shadow-sm">
      <view class="flex items-center text-30">
        <view class="i-ic-outline-info text-primary text-34"></view>
        <view class="text-gray-500 pl-2 leading-none">尚未登录，登录后可分析企业数据</view>
      </view>
      <view class="flex items-center">
        <text class="text-primary cursor-pointer hover:underline text-30" @click="userStore.isWxLoginLoading || navigate('/pages/login/index')">
          {{ userStore.isWxLoginLoading ? '自动登录中...' : '去登录' }}
        </text>
      </view>
    </view>

    <!--   -->
    <view class="bg-white px-4 py-1 flex items-center justify-between shadow-sm"
      v-if="showBindingPrompt && userStore.checkIsLoggedIn()">
      <view class="flex items-center text-30">
        <view class="i-ic-outline-info text-primary text-34"></view>
        <view class="text-gray-500 pl-2 leading-none">绑定微信体验完整功能</view>
      </view>
      <view class="flex items-center pr-2">
        <wd-button size="small" type="primary" open-type="getPhoneNumber" @getphonenumber="handleGetPhoneNumber"
          class="!h-56rpx !text-24rpx !px-24rpx">
          绑定微信
        </wd-button>
        <view class="i-ic-baseline-close text-primary text-34 ml-2 cursor-pointer" @click="showCloseModal"></view>
      </view>
    </view>
    <!-- 消息列表 -->
    <scroll-view ref="scrollRef" v-if="messages.length"
      :style="{ height:`calc(100vh - ${navbarHeight + textareaHeight + 10}px)` }"
      scroll-y class="box-border message-list"  :bounces="false" :scrollTop="scrollTop"
      :show-scrollbar="false" @scrolltoupper="handleScrollToUpper" :upper-threshold="50" @scroll="handleScroll"
      :scroll-with-animation="isMainTextareaed" :scroll-animation-duration="300"
      :enable-back-to-top="false"
      :scroll-into-view="scrollIntoViewId">
      <view>
        <!-- 加载更多消息的提示 -->
        <view v-if="isLoadingMoreMessages" class="py-2 flex justify-center items-center">
          <view class="loading-icon animate-spin mr-1 i-lucide-loader text-24 text-gray-500"></view>
          <text class="text-24 text-gray-500">加载更多消息...</text>
        </view>
        <view class="message-box px-35rpx py-2">
          <view v-for="(message, index) in messages" :key="`${index}-${message.id}`" :data-message-id="message.id" :class="`mb-30rpx message-bubble${'-'+message.type}`">
            <MessageBubble :message="message" :formattedTime="messageRelativeTime[message.id]"
              :loading="message.type === 'ai' && isLoading && index === messages.length - 1" :type="message.type"
              :loadingDuration="message.type === 'ai' && index === messages.length - 1 && loadingDuration ? loadingDuration : ''"
              @feedback="handleFeedback($event, message)" @thinking-updated="scrollToBottom"
              @regenerate="handleRegenerate" @edit-message="handleEditMessage">
              <template v-if="message.type === 'user'">
                <!-- 用户文本消息 -->
                <template v-if="!message.contentType || message.contentType === 'text'">
                  <text text="32" class="overflow-wrap-break-word leading-160%" user-select>{{ message.content }}</text>
                </template>

                <!-- 未知类型消息 -->
                <template v-else>
                  {{ message.content || '未知消息类型' }}
                </template>
              </template>
              <template v-else>
                <!-- 当AI消息有内容但没有markdown属性时显示骨架屏 -->
                <!-- <template v-if="message.content && message.markdown">
                  <MarkdownRenderer :content="message.markdown || message.content"
                    @suggested-question-selected="handleSuggestedQuestion" />
                </template>
                <template v-else-if="message.content && !message.markdown">
                    <view class="p-3">
                      <wd-skeleton theme="paragraph" animation="gradient" />
                    </view>
                </template> -->
                <MarkdownRenderer :content="message.markdown || message.content"
                    @suggested-question-selected="handleSuggestedQuestion" />
              </template>
            </MessageBubble>
          </view>
        </view>
      </view>


      <!-- 底部标记，用于滚动到底部 -->
      <view id="message-bottom" class="h-0 w-full"></view>
    </scroll-view>
    <template v-else>
      <view class="w-full h-full bg-#f5f5f5 py-4 px-50rpx box-border">
        <!-- 显示错误信息（当没有会话且有错误时） -->
        <view v-if="appError" class="bg-yellow-50 p-4 mb-4 rounded-lg border border-yellow-200">
          <view class="flex items-center mb-2">
            <view class="i-ic-baseline-warning text-yellow-500 text-40rpx mr-2"></view>
            <view class="text-yellow-700 font-medium text-32rpx">系统提示</view>
          </view>
          <view class="text-yellow-600 text-28rpx">{{ appError.message }}</view>
        </view>

        <!-- 正常开场白和推荐问题 -->
        <template v-if="appStore.parameters?.opening_statement">
          <view class="#232323 font-500 text-50 py-20rpx">{{ appStore.parameters.opening_statement ? appStore.parameters.opening_statement.split('||')[0] : '' }}</view>
          <view text="#666666 26" class="pb-4">{{ appStore.parameters.opening_statement ? appStore.parameters.opening_statement.split('||')[1] : '' }}
          </view>
        </template>
        <view v-show="appStore.parameters?.suggested_questions?.length">
          <view text="#676767 26" class="pt-4 b-t b-t-dashed b-t-#d8d8d8 suggested-questions-title"
            ref="suggestedQuestionsTitleRef">可以这样问我</view>
          <view class="flex flex-col flex-1">
            <scroll-view scroll-y class="py-2 w-full box-border" :style="{ height: suggestedQuestionsHeight || 'calc(100vh - 350rpx)' }">
              <view class="space-y-15rpx">
                <view v-for="(item, index) of appStore.parameters.suggested_questions" :key="index">
                  <text text="primary 30" @click="handleSend(item)" class="p-2 bg-white rd-2 inline-block">{{ item
                    }}</text>
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>


      <!-- 未登录时的提示内容 -->
      <view v-if="!userStore.checkIsLoggedIn()" :style="{ height: `calc(100vh - ${navbarHeight + textareaHeight}px)` }"
        class="flex flex-col items-center justify-center h-full box-border">
        <image class="w-120px h-120px mb-10rpx" src="https://fs1.weicheche.cn/images/test/250523065147-28421.png" />
        <view class="text-32 text-gray-500 text-center">登录后可查看聊天记录并体验完整功能</view>
        <view v-if="userStore.wxLoginError" class="text-28 text-gray-500 text-center">自动登录失败：{{ userStore.wxLoginError }}</view>
      </view>
    </template>
    <!-- 底部输入区域 - 固定定位，高度由内容自动撑开 -->
    <view class="w-full b-t b-t-solid b-t-#D8D8D8 box-border p-4 textarea fixed bottom-0 left-0 bg-#f5f5f5"
      style="padding-bottom: calc(env(safe-area-inset-bottom) + 12px); padding-bottom: calc(constant(safe-area-inset-bottom) + 12px)">

      <!-- :class="[isMainTextareaed ? 'border-primary' : 'border-transparent']" -->
      <view v-if="showExpandButton" class="flex box-border w-full absolute top-0 -translate-y-100% left-0 z-10">
        <view @click="openTextEditorPopup"
          class='bg-#f5f5f5 h-46 rd-rt flex-0 flex items-center space-x-1  b-1 b-solid border-_wn_D8D8D8 b-l-0 b-b-0 px-2 py-1 box-border'>
          <view class="i-lucide-circle-chevron-up text-32 text-#676767 cursor-pointer">
          </view>
          <view text="28 #676767">展开编辑</view>
        </view>
      </view>

      <view v-show="isLoading" text="#676767 center" class=" text-28 pb-2 -mt-16rpx thinking-text-animation">思考中...
      </view>

      <view class="relative bg-white rd-60rpx flex items-center p-3 border border-solid transition-all duration-100 "
        :class="[isMainTextareaed ? 'border-primary' : 'border-transparent']">
        <!-- 推荐问题按钮 -->
        <view v-if="false"
          class="i-material-symbols-book-2-outline-rounded flex items-center sapce-x-2 text-50 text-#999  relative -top-16rpx"
          @click="openRecommendedQuestionsPopup" />

        <view class="relative w-full">
          <!-- 自定义placeholder标签 -->
          <label for="mainTextarea"
            class="absolute left-0 top-0 pl-1 text-32 text-#999 leading-45rpx pointer-events-none z-1"
            :class="{ 'hidden': !isInputEmpty }">
            你有什么想知道的，快来问问我
          </label>
          <textarea maxlength="-1" id="mainTextarea" v-model="inputContent"  :focus="false" :auto-focus="false"
            :class="['w-full min-h-45 leading-45rpx text-32 resize-none max-h-200 pl-1 text-#232323']" auto-height
            :cursorSpacing="30" :show-confirm-bar="false" @keypress.enter.prevent="throttledHandleSendButtonClick()"
            @input="handleTextareaInput" @focus="handleMainTextarea" @blur="handleMainTextareaBlur" :disable-default-padding="true" />
        </view>
        <view class="flex items-center ml-2">
          <!-- 发送/停止按钮 -->
          <wd-button type="primary" class="!p-0 !w-70 !h-70 " size="small" round
            @click.stop="throttledHandleSendButtonClick"
            :disabled="isLoading ? false : (!inputContent?.trim() || !!appError?.message)">
            <!-- 发送图标 -->
            <view v-show="!isLoading" class='text-40 i-icon-park-outline-send'></view>
            <!-- 停止图标 - 在加载时显示 -->
            <view v-show="isLoading" class='text-40 i-ic-round-stop'></view>
          </wd-button>
        </view>
      </view>
    </view>

    <!-- 文本编辑弹出层 - 使用uni-popup -->
    <uni-popup ref="textEditorPopupRef" type="bottom" :safe-area="false">
      <view class="bg-white w-screen box-border p-4 rd-t-2">
        <view class="flex justify-between items-center mb-2">
          <view class="text-lg font-bold">编辑消息</view>
          <view class="text-18 p-16rpx i-ic-baseline-close" @click="closeTextEditorPopup">
          </view>
        </view>
        <view class="relative w-full">
          <!-- 自定义placeholder标签 -->
          <label for="popupTextarea"
            class="absolute left-0 top-0 p-3 text-28 text-#999 leading-45rpx pointer-events-none z-1"
            :class="{ 'hidden': !isInputEmpty }">
            你有什么想知道的，快来问问我
          </label>
          <textarea maxlength="-1" :cursorSpacing="20" id="popupTextarea"
            v-model="inputContent"
            :class="['w-full rd block border border-solid text-28 box-border p-3 h-50vh transition-all duration-100 outline-none', isPopupTextareaed ? 'border-primary' : 'border-#f5f5f5']"
            :show-confirm-bar="false" @focus="handlePopupTextarea" @blur="handlePopupTextareaBlur" :disable-default-padding="true" />
        </view>

        <!-- 发送按钮 -->
        <view class="mt-4 flex">
          <wd-button type="primary" class="flex-1" @click="throttledHandleSendButtonClick(); closeTextEditorPopup();">发送</wd-button>
        </view>
      </view>
    </uni-popup>
    <!-- 多模态输入相关功能已移除 -->



    <!-- 推荐问题弹出层 - 使用uni-popup -->
    <uni-popup ref="recommendedQuestionsRef" type="bottom" :safe-area="false" :mask-click="true">
      <RecommendedQuestions @select="handleSuggestedQuestion" @close="recommendedQuestionsRef.close()" />
    </uni-popup>

    <!-- 多模态输入相关组件已移除 -->
  </view>
  <!-- 历史记录弹出层 - 使用uni-popup -->
  <uni-popup ref="moreOptionsRef" type="left" :mask-click="true" :safe-area="false"
    mask-background-color="rgba(0, 0, 0, 0.8)">
    <more-options ref="moreOptionsComponent" @select-session="handleSelectSession" @close="moreOptionsRef.close()"
      @reset-conversation="handleNewSession" @reset-scroll="userHasScrolled = false" />
  </uni-popup>

</template>

<style>
page{
  background: #f5f5f5;
}
</style>

<style scoped>

page {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 思考中文案闪烁动画 */
.thinking-text-animation {
  animation: thinkingPulse 1.5s infinite ease-in-out;
}

@keyframes thinkingPulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }
}

/* 隐藏元素的工具类 */
.hidden {
  display: none;
}
</style>
