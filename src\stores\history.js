import { defineStore } from 'pinia'
import { ref, nextTick } from 'vue'
import { useChatStore } from './chat'
import { useUserStore } from './user'
import { fetchConversations, getMessages } from '../services/api' // 导入 getMessages
import { request } from '@/services/request'
import { parseMessageContent, parseAgentThoughts } from '@/utils/message-parser'
import { TEMP_ID } from '@/utils/constant/chat'

// 导入 towxml 实例
const towxmlInstance = require('../wxcomponents/towxml/index.js')

export const useHistoryStore = defineStore('history', () => {
  // 状态
  const sessions = ref([])
  const currentSession = ref(null) // 当前选中的会话对象
  const isLoadingMessages = ref(false) // 是否正在加载会话消息
  const messageError = ref(null) // 加载会话消息时的错误
  const hasMore = ref(true) // 是否有更多会话可加载
  const messagesHasMore = ref(false) // 是否有更多消息可加载

  // 保存历史记录到本地存储
  function saveToStorage() {
    try {
      uni.setStorageSync('chat_history', sessions.value)
    } catch (error) {
      console.error('保存历史记录失败:', error)
    }
  }

  // 为了兼容现有代码，添加saveSessionsToStorage方法
  function saveSessionsToStorage() {
    saveToStorage()
  }

  // 创建新会话
  function createNewSession() {
    const newSession = {
      sessionId: TEMP_ID+Date.now(),
      name: '新会话',  // 添加默认名称
      messages: [],
      timestamp: Date.now()
    }
    currentSession.value = newSession
    return newSession
  }

  // 重命名会话
  async function renameSession(sessionId, userInput = '') {
    const userStore = useUserStore();
    if (!userStore.token) {
      console.warn('未登录用户尝试重命名会话');
      throw new Error('请先登录再进行操作');
    }

    try {
      // 验证输入是否为空
      if (!userInput || userInput.trim() === '') {
        throw new Error('名称不能为空');
      }

      // 新增：截断输入到最大长度
      const maxLen = 50;
      if (userInput.length > maxLen) {
        userInput = userInput.substring(0, maxLen);
        console.log('会话名称已自动截断至', maxLen, '字符');
      }

      // 调用重命名API
      const response = await request(
        `/v1/conversations/${sessionId}/name`,
        {
          method: 'POST',
          header: {
            'Content-Type': 'application/json'
          },
          data: {
            name: userInput,
            auto_generate: false
          }
        }
      );

      // 检查响应状态
      if (!response || response.statusCode >= 400) {
        throw new Error('服务器响应异常');
      }

      // 重新加载会话列表获取最新数据
      await loadConversations();

      // 更新当前会话名称（如果是当前会话）
      if (currentSession.value && currentSession.value.sessionId === sessionId) {
        currentSession.value.name = userInput;
      }

      // 更新会话列表中的名称
      const sessionIndex = sessions.value.findIndex(s => s.sessionId === sessionId);
      if (sessionIndex !== -1) {
        sessions.value[sessionIndex].name = userInput;
      }

      // 保存到本地存储
      saveToStorage();

      return true; // 返回成功状态
    } catch (error) {
      console.error('重命名会话失败:', error);
      throw error; // 将错误向上抛出，以便调用者处理
    }
  }

  // 添加消息到当前会话
  function addMessage(message) {
    try {
      // 验证消息格式
      if (!message || !message.role || !message.content) {
        console.error('无效的消息格式:', message)
        return false
      }

      // 如果没有当前会话，创建新会话
      if (!currentSession.value) {
        createNewSession()
      }

      if (currentSession.value) {
        // 添加消息到当前会话
        currentSession.value.messages.push({
          ...message,
          timestamp: message.timestamp || Date.now()
        })
        return true
      }

      return false
    } catch (error) {
      console.error('添加消息失败:', error)
      return false
    }
  }

  // 处理会话创建和重命名
  async function handleConversationCreated(rawData) {
    try {
      // 检查是否存在conversation_id
      if (rawData.conversation_id && currentSession.value) {
        // 如果会话ID已经存在且相同，说明已经处理过，直接返回
        if (currentSession.value.sessionId === rawData.conversation_id) {
          return;
        }

        // 更新当前会话的ID
        currentSession.value.sessionId = rawData.conversation_id;

        // 如果这是第一条消息，进行重命名
        if (currentSession.value.messages.length === 1) {
          // 获取用户的第一条消息内容
          const firstMessage = currentSession.value.messages[0];
          const userInput = firstMessage?.content || '';
          await renameSession(rawData.conversation_id, userInput);
        }

        // 只有在会话有消息时才保存
        if (currentSession.value.messages.length > 0) {
          saveCurrentSession();
        }
      }
    } catch (error) {
      console.error('处理会话创建失败:', error);
    }
  }

  // 保存当前会话到历史记录
  function saveCurrentSession() {
    if (currentSession.value && currentSession.value.messages.length > 0) {
      // 检查是否已存在此会话，如存在则更新
      const existingIndex = sessions.value.findIndex(
        session => session.sessionId === currentSession.value?.sessionId
      )

      if (existingIndex >= 0) {
        // 更新现有会话，但保留原有的位置
        sessions.value[existingIndex] = { ...currentSession.value }
      } else {
        // 检查是否有相同时间戳的会话
        const sameTimeIndex = sessions.value.findIndex(
          session => session.timestamp === currentSession.value.timestamp
        )

        if (sameTimeIndex >= 0) {
          // 如果找到相同时间戳的会话，更新它
          sessions.value[sameTimeIndex] = { ...currentSession.value }
        } else {
          // 添加新会话到列表开头
          sessions.value.unshift({ ...currentSession.value })
        }
      }

      // 保存到本地存储
      saveToStorage()
    }
  }

  // 删除会话
  async function deleteSession(sessionId) {
    try {
      // 调用删除会话API
      await request(`/v1/conversations/${sessionId}`, {
        method: 'DELETE'
      });

      // 从本地状态中移除会话
      sessions.value = sessions.value.filter(session => session.sessionId !== sessionId)

      // 如果删除的是当前会话，清空当前会话
      if (currentSession.value && currentSession.value.sessionId === sessionId) {
        currentSession.value = null;
      }

      // 保存到本地存储
      saveToStorage()

      return true;
    } catch (error) {
      console.error('删除会话失败:', error);
      throw error;
    }
  }

  // 获取或设置当前会话
  function getCurrentSession(session = null) {
    if (session) {
      currentSession.value = session
    }
    return currentSession.value
  }

  // 加载会话列表 Action
  async function loadConversations(limit = 20, last_id = null) {
    const userStore = useUserStore()
    console.log("🚀 ~ loadConversations ~ userStore.token:", userStore.token)
    if (!userStore.token) {
      return { data: [], has_more: false }
    }

    try {
      console.log('开始加载会话列表...', { limit, last_id })
      const conversationList = await fetchConversations(limit, last_id) // 调用 API 并传入分页参数
      console.log('获取到的会话列表:', conversationList)

      // 准备返回数据
      const result = conversationList

      if (Array.isArray(result.data)) {
        // 转换数据格式
        const newSessions = result.data.map(conv => ({
          sessionId: conv.id,
          name: conv.name || `会话 ${conv.id.slice(0, 5)}`,
          messages: [], // 初始为空，需要时再加载具体消息
          timestamp: conv.created_at ? new Date(conv.created_at * 1000).getTime() : Date.now()
        })).sort((a, b) => b.timestamp - a.timestamp);

        // 更新当前会话的名称（如果存在）
        if (currentSession.value) {
          const updatedSession = newSessions.find(s => s.sessionId === currentSession.value.sessionId);
          if (updatedSession) {
            currentSession.value.name = updatedSession.name;
          }
        }

        // 处理分页追加逻辑
        if (last_id) {
          sessions.value = [...sessions.value, ...newSessions];
        } else {
          sessions.value = newSessions;
        }

        console.log('会话列表已更新:', sessions.value);

        // 保存到本地存储
        saveToStorage();
      } else {
        console.warn('fetchConversations 返回的不是数组:', conversationList);
        if (!last_id) {
          // 只有在不是加载更多的情况下才清空
          sessions.value = [];
        }
        result.data = [];
      }

      // 更新hasMore状态
      hasMore.value = result.has_more || false

      // 返回结果供调用者使用
      return result;
    } catch (error) {
      console.error('加载会话列表失败:', error);

      // 显示错误提示
      // uni.showToast({
      //   title: '加载会话列表失败',
      //   icon: 'none'
      // });

      return { data: [], has_more: false };
    }
  }

  // 加载指定会话的消息 Action
  async function fetchMessagesByConversationId(conversationId, first_id = null, shouldAppend = false) {
    if (!conversationId || conversationId.includes(TEMP_ID)) {
      console.log('fetchMessagesByConversationId: conversationId is required')
      return []
    }

    first_id= first_id?first_id.includes(TEMP_ID)?'':first_id:'';

    isLoadingMessages.value = true
    messageError.value = null
    const chatStore = useChatStore()

    // 只有在不是加载更多消息时才清空当前聊天界面的消息
    // if (!shouldAppend) {
    //   chatStore.clearMessages()
    // }

    try {
      console.log(`获取会话消息: ${conversationId}, first_id: ${first_id}, shouldAppend: ${shouldAppend}`)
      const messagesData = await getMessages(conversationId, first_id)
      console.log('获取到的消息:', messagesData)

      // 更新messagesHasMore状态
      messagesHasMore.value = messagesData.has_more || false
      console.log('更新messagesHasMore状态:', messagesHasMore.value)

      // 格式化消息数据
      const formattedMessages = messagesData.data.flatMap(item => {
        const messages = []
        // 添加用户消息
        messages.push({
          ...item,
          id: `${item.id}`,
          role: 'user',
          type: 'user', // 添加type字段与MessageBubble组件保持一致
          contentType: 'text',
          content: item.query,
          timestamp: item.created_at ? new Date(item.created_at * 1000).getTime() : Date.now()
        })

        // 添加AI回复消息
        // 如果有answer或者agent_thoughts，添加AI消息
        let aiContent = item.answer || '';

        // 检查是否存在action_input属性
        aiContent = parseMessageContent(aiContent);

        // 如果answer为空，尝试从agent_thoughts中获取所有thought内容并按顺序拼接
        if (!aiContent && item.agent_thoughts && item.agent_thoughts.length > 0) {
          aiContent = parseAgentThoughts(item.agent_thoughts);
        }

        // 只有当有内容时才添加AI消息
        if (aiContent) {
          // 过滤内容中的特殊标签
          const filteredContent = aiContent
            .replace(/<think[\s\S]*?<\/think>/g, '')
            .replace(/<th[\s\S]*?<\/th/g, '')
            .replace(/<think[\s\S]*?<\/th/g, '')
            .replace(/<th[\s\S]*?<\/think>/g, '')
            .replace(/<(think|th)[\s\S]*$/g, '');
          
          // 将内容转换为markdown
          let markdownContent = '';
          try {
            markdownContent = towxmlInstance(filteredContent, 'markdown', {
              theme: 'light',
              events: { /* 事件处理通常在渲染器中 */ }
            });
            console.log('生成AI消息的Markdown成功:', item.id);
          } catch (error) {
            console.error('处理Markdown失败:', error);
          }
          
          messages.push({
            ...item,
            id: `${item.id}`,
            role: 'assistant',
            type: 'ai', // 添加type字段与MessageBubble组件保持一致
            contentType: 'text',
            content: filteredContent,
            markdown: markdownContent,
            agent_thoughts: item.agent_thoughts, // 保留agent_thoughts字段以便后续处理
            timestamp: item.created_at ? new Date(item.created_at * 1000).getTime() + 1 : Date.now() + 1
          })
        }
        return messages
      }).sort((a, b) => a.timestamp - b.timestamp)

      // 初始化用于返回的变量
      let allMessages = formattedMessages;
      let firstMessageId = null;

      // 如果是加载更多消息，则将新消息添加到现有消息中
      if (shouldAppend && chatStore.messages.length > 0) {
        // 将新消息添加到现有消息的前面
        allMessages = [...formattedMessages, ...chatStore.messages]
        chatStore.setMessages(allMessages)

        // 更新当前会话的消息
        if (currentSession.value) {
          currentSession.value.messages = allMessages

          // 更新sessions中的会话
          const index = sessions.value.findIndex(s => s.sessionId === conversationId)
          if (index !== -1) {
            sessions.value[index] = currentSession.value
          }
        }

        // 获取合并后消息数组的第一条消息的ID
        firstMessageId = allMessages.length > 0 ? allMessages[0].id : null;
      } else {
        // 如果是首次加载或刷新，直接设置消息
        chatStore.setMessages(formattedMessages)

        // 获取新加载消息数组的第一条消息的ID
        firstMessageId = formattedMessages.length > 0 ? formattedMessages[0].id : null;

        // 等待 DOM 更新
        await nextTick()

        // 更新当前会话
        const targetSession = sessions.value.find(s => s.sessionId === conversationId)
        if (targetSession) {
          currentSession.value = { ...targetSession, messages: formattedMessages }
          // 更新sessions中的会话
          const index = sessions.value.findIndex(s => s.sessionId === conversationId)
          if (index !== -1) {
            sessions.value[index] = currentSession.value
          }
        } else {
          currentSession.value = {
            sessionId: conversationId,
            name: `会话 ${conversationId.slice(0, 5)}`,
            messages: formattedMessages,
            timestamp: Date.now()
          }
          // 添加到sessions
          sessions.value.unshift(currentSession.value)
        }
      }

      // 保存到本地存储
      saveToStorage()

      // 返回是否还有更多消息和格式化后的消息
      return {
        messages: formattedMessages,
        has_more: messagesData.has_more || false,
        first_id: firstMessageId
      }
    } catch (error) {
      console.error('加载会话消息失败:', error)
      messageError.value = error.message || '加载消息失败'

      // 出错时设置messagesHasMore为false
      messagesHasMore.value = false

      // 只有在不是加载更多消息时才清空消息列表
      if (!shouldAppend) {
        chatStore.setMessages([]) // 清空或显示错误提示
      }

      // 即使出错也返回一致的结构
      return {
        messages: [],
        has_more: false,
        first_id: null
      }
    } finally {
      isLoadingMessages.value = false
    }
  }

  // 清空历史会话数据
  function clearHistory() {
    sessions.value = []
    currentSession.value = null
    uni.removeStorageSync('chat_history')
  }

  return {
    // 状态
    sessions,
    currentSession,
    hasMore,
    messagesHasMore,

    // 方法
    addMessage,
    createNewSession,
    saveCurrentSession,
    deleteSession,
    getCurrentSession,
    saveSessionsToStorage,
    loadConversations, // 加载会话列表
    fetchMessagesByConversationId, // 加载指定会话的消息
    renameSession, // 导出重命名方法以便其他地方使用
    handleConversationCreated, // 导出新方法
    clearHistory // 导出清理方法
  }
})