# UI 优化设计方案

## 设计原则
1. **现代极简**：采用shadcn/ui的几何造型与微交互设计
2. **一致性**：组件遵循8px基线网格规范
3. **可访问性**：满足WCAG 2.1 AA标准

## 颜色系统
```json
// unocss.config.js 颜色扩展
colors: {
  primary: {
    50: '#f0fdf0',
    100: '#dcfce7',
    200: '#bbf7d0',
    300: '#38C948', // 品牌主色
    400: '#4ade80',
    500: '#22c55e'
  },
  slate: {
    50: '#f8fafc',
    100: '#f1f5f9',
    // ...中性色梯度
  }
}
```

## 布局规范
### 栅格系统
```vue
<!-- 响应式布局示例 -->
<div class="grid grid-cols-1 md:grid-cols-12 gap-4">
  <aside class="md:col-span-3">...</aside>
  <main class="md:col-span-9">...</main>
</div>
```

## 组件库架构
```
src/components/ui/
├── button/
│   ├── Button.vue       // 基础按钮
│   └── variants.js      // 变体配置
├── card/
└── index.js            // 统一导出
```

## 实施路线
1. [阶段一] 颜色系统迁移（2天）
2. [阶段二] 基础组件开发（5天）
3. [阶段三] 页面重构（3天）

## 代码质量保障
- 新增Storybook可视化测试
- 添加chromatic视觉回归测试