<script setup>
import { navigate } from '@/utils/navigate'
import { ref, reactive } from 'vue'
import { useUserStore } from '@/stores/user'
import { useHistoryStore } from '@/stores/history'
import { toast, loading, hideLoading } from '@/utils/interface'

import { request } from '@/services/request'

// 用户Store
const userStore = useUserStore()

// 表单数据
const loginForm = reactive({
  // username: 'superadmin',
  // password: '7twr4mknc69beahq',
  username: '',
  password: '',
  agreed: false // 新增协议同意状态
})

// 表单验证状态
const formErrors = reactive({
  username: '',
  password: ''
})

// 登录状态
const isLoggingIn = ref(false)
// 密码可见状态
const showPassword = ref(false)

// 商户选择器相关
const merchantOptions = ref([])
const selectedMerchant = ref(null)
const showMerchantSelect = ref(false)

// 微信登录相关
const isWxLoggingIn = ref(false)

// 处理微信一键登录
const handleWxLogin = async (e) => {
  console.log("🚀 ~ handleWxLogin ~ e:", e)
  if (isWxLoggingIn.value) return
  isWxLoggingIn.value = true

  try {
    if (e.errMsg !== 'getPhoneNumber:ok') {
      throw new Error('用户拒绝授权手机号')
    }

    const { encryptedData, iv } = e
    if (!encryptedData || !iv) {
      throw new Error('获取微信授权信息失败')
    }

    const loginRes = await new Promise((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject
      })
    })

    loading('登录中...')

    const res = await request('/v1/wxMiniOneKeyLogin', {
      method: 'POST',
      data: {
        code: loginRes.code,
        encryptedData,
        iv
      }
    })

    if (res.status !== 200) {
      throw new Error(res?.msg || '登录失败，请重试')
    }

    // 保存用户信息和token
    userStore.setAccountInfo(res.data.account)
    userStore.setToken(res.data.token)
    userStore.setExpire(res.data.expire)
    userStore.setIsBind(true)
    if (res.data.merchants) {
      userStore.setMerchantList(res.data.merchants)
      if(res.data.merchants.length === 1){
        try{
          userStore.switchMerchant(res.data.merchants[0])
        }catch(error){
          toast(error.message ? error.message : `商户切换失败: 未知错误`, 'none')
          return;
        }
      }
    }
    userStore.setManuallyLoggedOut(false)

    // 加载历史会话
    const historyStore = useHistoryStore()
    await historyStore.loadConversations()

    // 返回上一页
    uni.navigateBack()
    toast('登录成功', 'success')
    setTimeout(() => {
      hideLoading()
    }, 3000)
  } catch (error) {
    console.error('微信登录失败:', error)
    toast(error.message.includes('login') ? '获取微信登录code失败' : error.message || '登录失败，请重试')
  } finally {
    isWxLoggingIn.value = false
  }
}
// 验证用户名
const validateUsername = () => {
  if (!loginForm.username.trim()) {
    formErrors.username = '请输入用户名'
    return false
  }
  formErrors.username = ''
  return true
}

// 验证密码
const validatePassword = () => {
  if (!loginForm.password) {
    formErrors.password = '请输入密码'
    return false
  } else if (loginForm.password.length < 6) {
    formErrors.password = '密码不能少于6个字符'
    return false
  }
  formErrors.password = ''
  return true
}

// 表单验证
const validateForm = () => {
  const isUsernameValid = validateUsername()
  const isPasswordValid = validatePassword()
  return isUsernameValid && isPasswordValid
}

// 处理商户选择
const handleMerchantSelect = async (value) => {
  const selected = value
  try {
    isLoggingIn.value = true

    // 获取微信code
    let wxCode = ''
    try {
      const loginRes = await uni.login({
        provider: 'weixin'
      })
      wxCode = loginRes.code
    } catch (error) {
      console.error('获取微信code失败:', error)
      toast('微信登录失败，请重试')
      isLoggingIn.value = false
      return
    }
    loading('登录中...')

    const res = await request('/v1/login', {
      method: 'POST',
      data: {
        username: loginForm.username,
        password: loginForm.password,
        merchant_type: selected.merchant_type,
        merchant_id: selected.merchant_id,
        code: wxCode
      }
    })

    // 保存用户信息和token
    userStore.setAccountInfo(res.data.account)
    userStore.setToken(res.data.token)
    userStore.setExpire(res.data.expire)
    userStore.setIsBind(res.data.isBind)
    userStore.setMerchantInfo({
      merchant_type: selected.merchant_type,
      merchant_id: selected.merchant_id
    })
    userStore.setManuallyLoggedOut(false)

    // 加载历史会话
    const historyStore = useHistoryStore()
    await historyStore.loadConversations()
    hideLoading()
    // 关闭弹窗并返回上一页
    showMerchantSelect.value = false
    uni.navigateBack()
  } catch (error) {
    showMerchantSelect.value = false
    if (error.response?.status === 400) {
      toast('商户信息无效，请重新选择')
    } else {
      toast(error.message || '商户登录失败')
    }
    isLoggingIn.value = false
  }
}

// 账号密码登录
const handleLogin = async () => {
  selectedMerchant.value = null // 重置商户选择状态
  if (!validateForm()) return

  if (!loginForm.agreed) {
    uni.showModal({
      title: '用户协议',
      content: '请阅读并同意用户服务协议和隐私政策',
      confirmText: '同意',
      success: (res) => {
        if (res.confirm) {
          loginForm.agreed = true
          handleLogin() // 重新触发登录
        }
      }
    })
    return
  }

  try {
    isLoggingIn.value = true
    loading('获取商户中...')
    // 获取商户列表
    const merchantRes = await request('/v1/getMerchants', {
      method: 'POST',
      data: { account: loginForm.username }
    })
    hideLoading()
    // 存储商户列表
    userStore.setMerchantList(merchantRes.data)

    // 处理商户选择逻辑
    merchantOptions.value = merchantRes.data.map(m => ({
      label: m.label,
      value: m.value,
      merchant_type: m.merchant_type,
      merchant_id: m.merchant_id
    }))
    console.log("🚀 ~ handleLogin ~ merchantRes.data?.length:", merchantRes.data?.length)

    if (merchantRes.data?.length > 1) {
      // 多个商户显示选择器
      showMerchantSelect.value = true
    } else if (merchantRes.data?.length === 1) {
      // 单个商户直接选中
      handleMerchantSelect(merchantRes.data[0])
    } else {
      // 无商户直接跳转
      const historyStore = useHistoryStore()
      await historyStore.loadConversations()
      uni.navigateBack()
    }
  } catch (error) {
    if (error.response?.status === 503) {
      toast('服务不可用，请稍后重试')
    } else {
      toast(error.message || '获取商户信息失败')
    }
  } finally {
    isLoggingIn.value = false
  }
}
</script>

<template>
  <view class="login-container bg-gradient-to-b from-gray-50 to-white dark:from-gray-900 dark:to-gray-800">
    <view class="login-content p-8 box-border flex flex-col justify-center">
      <view class="app-logo">
        <image src="https://fs1.weicheche.cn/images/test/250523065147-28421.png" mode="aspectFit" class="logo-image" />
        <view class="app-name">喂车车智能助手</view>
        <view class="text-32 text-#999 pt-4">请使用您的账号密码登录系统</view>
      </view>

      <view class="login-form">
        <view class="form-item">
          <view class="relative">
            <input v-model="loginForm.username" placeholder="请输入用户名"
              class="h-100rpx text-32 leading-100rpx pl-4 pr-8 border border-#D8D8D8 border-solid rounded-lg text-30 placeholder:text-30"
              :class="{ 'border-red-500': formErrors.username }" :disabled="isLoggingIn" @blur="validateUsername()"
              @input="formErrors.username && validateUsername()" />
          </view>
          <view class="error-message text-red-500 text-sm " :class="{ 'visible': formErrors.username }">{{
            formErrors.username || ' ' }}</view>
        </view>

        <view class="form-item">
          <view class="relative">
            <input v-model="loginForm.password" :password="!showPassword" placeholder="请输入密码"
              class="h-100rpx text-32 leading-100rpx pl-4 pr-8 border border-#D8D8D8 border-solid rounded-lg text-30 placeholder:text-30"
              :class="{ 'border-red-500': formErrors.password }" :disabled="isLoggingIn" @blur="validatePassword()"
              @input="formErrors.password && validatePassword()" />
            <view
              class="absolute right-16 top-1/2 z-10 text-36 transform -translate-y-1/2 cursor-pointer flex items-center"
              @click.capture.stop="showPassword = !showPassword">
              <view :class="showPassword ? 'i-mdi-eye-outline' : 'i-mdi-eye-off-outline'"
                class="text-xl text-#999 flex items-center justify-center">
              </view>
            </view>
          </view>
          <view class="error-message text-red-500 text-sm " :class="{ 'visible': formErrors.password }">{{
            formErrors.password || ' ' }}</view>
        </view>

        <view class="mt-6">
          <wd-button plain @click="handleLogin" class="w-full !text-32rpx h-100rpx flex items-center justify-center !rd-lg"
            type="primary">
            登录
          </wd-button>

          <!-- 分割线 -->
          <view class="flex items-center justify-center w-full my-6">
            <view class="flex-1 h-1px bg-#e5e5e5"></view>
            <view class="px-3 text-32 text-#999">或</view>
            <view class="flex-1 h-1px bg-#e5e5e5"></view>
          </view>

          <!-- 微信一键登录按钮 -->
          <wd-button type="primary" class="w-full !rd !text-32rpx h-100rpx flex items-center justify-center !rd-lg"
            open-type="agreePrivacyAuthorization|getPhoneNumber" @agreeprivacyauthorization="loginForm.agreed = true"
            @getphonenumber="handleWxLogin">
            <view class="flex items-center justify-center">
              <text text="32">手机号一键登录</text>
            </view>
          </wd-button>
        </view>
      </view>
      <view class="flex items-center justify-center text-28 text-#999"  @click="loginForm.agreed = !loginForm.agreed">
        <i :class="loginForm.agreed ? 'i-ic-twotone-check-circle-outline text-primary' : 'i-ic-outline-circle'"
          class="w-34 h-34 mr-1" />
        <text class="pl-4rpx">我已阅读并同意</text>
        <view class="text-primary inline" @click.capture.stop="navigate('/pages/login/usagePolicy')">《用户协议》</view>
        <text class="">和</text>
        <view class="text-primary inline" @click.capture.stop="navigate('/pages/login/privacy')">《隐私政策》</view>
      </view>
    </view>

    <!-- 商户选择弹窗 -->
    <MerchantSelect v-model:value="selectedMerchant" v-model:show="showMerchantSelect" @select="handleMerchantSelect" />
  </view>
</template>

<style scoped>
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.login-header {
  position: relative;
  padding: 20px;
  display: flex;
  align-items: center;
}

.back-button {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.app-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}

.logo-image {
  width: 120px;
  height: 120px;
}

.app-name {
  font-size: 38rpx;
  font-weight: 600;
  color: #333;
}

.login-form {
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 15rpx;
  /* Reduced spacing between form items */
  width: 100%;
  position: relative;
  /* For absolute positioning of error messages */
}

/* Ensure input text is vertically centered */
input {
  line-height: 100rpx !important;
  height: 100rpx !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  box-sizing: border-box;
}

/* Ensure placeholder text is vertically centered */
input::placeholder {
  line-height: 100rpx !important;
}

/* Additional placeholder styling for different browsers */
input::-webkit-input-placeholder {
  line-height: 100rpx !important;
}

input:-moz-placeholder {
  line-height: 100rpx !important;
}

/* Ensure button text is centered */
.wd-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100rpx !important;
  line-height: 100rpx !important;
}

.full-width-btn {
  width: 100%;
}

.form-helper {
  margin-top: 20px;
  text-align: center;
}

.register-link {
  color: #38C948;
  font-size: 14px;
}


.footer-text {
  font-size: 12px;
  color: #999;
}

.merchant-popup {
  background-color: #fff;
  border-radius: 16px 16px 0 0;
  padding: 20px;
  max-height: 60vh;
}

.popup-header {
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
  margin-bottom: 16px;
}

.popup-title {
  font-size: 18px;
  font-weight: 600;
  text-align: center;
}

.merchant-list {
  max-height: 50vh;
}

.merchant-item {
  padding: 16px 0;
  border-bottom: 1px solid #f5f5f5;
}

.merchant-item:active {
  background-color: #f5f5f5;
}

.current-user {
  margin-bottom: 16px;
  font-size: 14px;
  color: #999;
  text-align: center;
}

.error-message {
  min-height: 16px;
  /* Reduced fixed height for error messages */
  opacity: 0;
  /* Hidden by default */
  transition: opacity 0.2s ease;
  /* Smooth transition */
  font-size: 12px;
  /* Smaller font size */
}

.error-message.visible {
  opacity: 1;
  /* Show when has content */
}
</style>