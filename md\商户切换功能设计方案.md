# 商户切换功能设计方案

## 1. 功能背景
- 用户登录后可能关联多个商户
- 需要在首页提供商户切换能力
- 切换后需更新全局状态和token

## 2. 技术方案

### 2.1 数据流设计
```mermaid
flowchart TD
    A[用户界面] -->|商户选择| B[用户Store]
    B -->|调用API| C[后端服务]
    C -->|返回新token| B
    B -->|更新状态| A
```

### 2.2 核心接口
- 路径：POST /v1/switchOstn
- 参数：
  ```json
  {
    "merchant_type": 1,
    "merchant_id": 123
  }
  ```
- 响应：
  ```json
  {
    "token": "新token",
    "expire": 1582166642
  }
  ```

### 2.3 组件改造
```vue
<template>
  <!-- 替换原有AI智能助手文案 -->
  <view class="merchant-switcher">
    <uni-data-picker
      v-if="isLoggedIn && merchants.length > 1"
      :localdata="merchants"
      v-model="currentMerchant"
      @change="handleMerchantChange"
    />
    <view v-else-if="isLoggedIn" class="single-merchant">
      {{ currentMerchant.name }}
    </view>
    <view v-else>AI智能助手</view>
  </view>
</template>
```

### 2.4 Store增强
```javascript
// src/stores/user.js
const switchMerchant = async (merchant) => {
  try {
    const res = await api.switchOstn({
      merchant_type: merchant.type,
      merchant_id: merchant.id
    })
    
    // 更新token和过期时间
    setToken(res.data.token)
    setExpire(res.data.expire)
    setMerchantInfo(merchant)
    
    // 显示切换成功
    uni.showToast({ title: '商户切换成功' })
    return true
  } catch (error) {
    uni.showToast({ title: '商户切换失败', icon: 'error' })
    return false
  }
}
```

## 3. 样式方案
使用Unocss定义样式：
```css
/* 商户选择器 */
.merchant-switcher {
  @apply px-4 py-2 flex items-center;
}

/* 单商户显示 */
.single-merchant {
  @apply text-gray-800 font-medium;
}
```

## 4. 测试用例
| 测试场景 | 预期结果 |
|---------|---------|
| 未登录状态 | 显示"AI智能助手" |
| 单商户用户 | 显示商户名称 |
| 多商户用户 | 显示选择器 |
| 切换成功 | 更新token和商户信息 |
| 切换失败 | 保持原商户，显示错误提示 |

## 5. 后续优化
1. 添加商户切换loading状态
2. 实现商户信息缓存
3. 添加切换历史记录功能