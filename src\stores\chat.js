import { defineStore } from 'pinia'
import { ref } from 'vue'
import { TEMP_ID } from '@/utils/constant/chat'

export const useChatStore = defineStore('chat', () => {
  // 状态
  const messages = ref([])
  const isLoading = ref(false)

  // 添加消息
  const addMessage = (message) => {
    // 如果消息中已经有id，则使用该id，否则生成一个时间戳作为id
    const id = message.id || TEMP_ID+Date.now();
    // 如果消息中没有时间戳，才设置当前时间为时间戳
    const timestamp = message.timestamp || Date.now();

    // 创建新消息对象，确保时间戳不会被覆盖
    const newMessage = {
      id,
      timestamp,
      ...message
    };

    // 打印日志，便于调试
    console.log('添加消息:', newMessage.type, newMessage.id, '时间戳:', new Date(newMessage.timestamp).toLocaleString());

    messages.value.push(newMessage);
    return id; // 返回使用的id，便于后续更新
  }

  // 添加用户文本消息
  const addUserMessage = (content, id = null) => {
    return addMessage({
      type: 'user',
      contentType: 'text',
      content,
      ...(id ? { id } : {}) // 如果提供了id，则使用提供的id
    })
  }

  // 添加AI消息
  const addAIMessage = (content, id = null) => {
    return addMessage({
      type: 'ai',
      contentType: 'text',
      conversation_id:'',
      content,
      ...(id ? { id } : {}) // 如果提供了id，则使用提供的id
    })
  }

  // 添加用户文件消息
  const addUserFileMessage = (fileInfo) => {
    addMessage({
      type: 'user',
      contentType: 'file',
      content: `[文件] ${fileInfo.name}`,
      media: {
        type: 'file',
        file: fileInfo
      }
    })
  }

  // 设置加载状态
  const setLoading = (status) => {
    isLoading.value = status
  }

  // 设置消息列表
  const setMessages = (newMessages) => {
    messages.value = newMessages
  }

  // 清空消息
  const clearMessages = () => {
    messages.value = []
  }

  // 删除消息
  const deleteMessage = (messageId) => {
    const index = messages.value.findIndex(msg => msg.id === messageId)
    if (index !== -1) {
      messages.value.splice(index, 1)
    }
  }

  // 保存历史消息
  const saveHistoryMessages = () => {
    uni.setStorageSync('chatHistory', JSON.stringify(messages.value))
  }

  return {
    // 状态
    messages,
    isLoading,
    // 方法
    addMessage,
    addUserMessage,
    addAIMessage,
    addUserFileMessage,
    setLoading,
    setMessages,
    clearMessages,
    deleteMessage,
    saveHistoryMessages
  }
})