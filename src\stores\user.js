import { defineStore } from 'pinia'
import * as api from '@/services/api'
import { request } from '@/services/request'
import { ref, computed } from 'vue'
import { useHistoryStore } from './history'
import { useChatStore } from './chat'
import { useApplicationStore } from './application'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref('')
  const userInfo = ref(null)
  const merchantInfo = ref(null)
  const merchantList = ref([])
  const voicePlayEnabled = ref(true)
  const isBind = ref(false)
  const manuallyLoggedOut = ref(false)
  const isWxLoginLoading = ref(false)
  const wxLoginError = ref('')

  // 检查用户是否已登录
  const checkIsLoggedIn = () => {
    return !!token.value
  }

  // 获取本地存储的用户信息
  const initUserState = () => {
    const storedToken = uni.getStorageSync('token') || ''
    const storedUserInfo = uni.getStorageSync('userInfo') || null
    const storedMerchantInfo = uni.getStorageSync('merchantInfo') || null
    const storedMerchantList = uni.getStorageSync('merchantList') || []
    const storedVoicePlayEnabled = uni.getStorageSync('voicePlayEnabled')
    const storedIsBind = uni.getStorageSync('isBind')
    const storedManuallyLoggedOut = uni.getStorageSync('manuallyLoggedOut')

    token.value = storedToken
    merchantList.value = storedMerchantList
    voicePlayEnabled.value = storedVoicePlayEnabled !== undefined ? storedVoicePlayEnabled : true
    isBind.value = storedIsBind !== undefined ? storedIsBind : false
    manuallyLoggedOut.value = storedManuallyLoggedOut !== undefined ? storedManuallyLoggedOut : false

    if (storedToken && storedUserInfo) {
      userInfo.value = { ...storedUserInfo, token: storedToken }
      merchantInfo.value = storedMerchantInfo
    }
  }

  // 设置语音播放开关状态
  const setVoicePlayEnabled = (enabled) => {
    voicePlayEnabled.value = enabled
    uni.setStorageSync('voicePlayEnabled', enabled)
  }

  // 设置微信绑定状态
  const setIsBind = (bindStatus) => {
    isBind.value = bindStatus
    uni.setStorageSync('isBind', bindStatus)
  }

  // 设置用户信息
  const setUserInfo = (info) => {
    userInfo.value = info
    uni.setStorageSync('userInfo', info)
  }

  // 设置token
  const setToken = (newToken) => {
    token.value = newToken
    userInfo.value = { ...userInfo.value, token: newToken }
    uni.setStorageSync('token', newToken)
  }

  // 设置商户信息
  const setMerchantInfo = (info) => {
    merchantInfo.value = info
    uni.setStorageSync('merchantInfo', info)
  }

  // 设置商户列表
  const setMerchantList = (list) => {
    merchantList.value = list
    uni.setStorageSync('merchantList', list)
  }

  // 获取商户列表
  const getMerchantList = () => {
    return merchantList.value
  }

  // 获取当前商户
  const getCurrentMerchant = () => {
    return merchantInfo.value?.merchant_id || null
  }
  // 获取当前商户对象
  const currentMerchantObject = computed(() => {
    return merchantList.value.find(item => item?.merchant_id === getCurrentMerchant())
  })

  // 切换商户
  const switchMerchant = async (params) => {
    try {
      const res = await api.switchMerchant({
        merchant_id: params.merchant_id,
        merchant_type: params.merchant_type || 1, // 默认为油站类型
        token: token.value
      })
      console.log("🚀 ~ switchMerchant ~ res:", res)

      if (res.status === 200) {
        // 从商户列表中查找对应商户
        const merchant = merchantList.value.find(m => m.merchant_id === params.merchant_id)
        if (merchant) {
          setMerchantInfo(merchant)
        }
        setToken(res.data.token) // 更新token
        setExpire(res.data.expire) // 设置过期时间
        return true
      }
      return false
    } catch (error) {
      throw error
    }
  }

  // 设置账户信息
  const setAccountInfo = (account) => {
    userInfo.value = account
    uni.setStorageSync('userInfo', account)
  }

  // 设置token过期时间
  const setExpire = (expire) => {
    uni.setStorageSync('expire', expire)
  }

  // 微信小程序静默登录
  const wxMiniLogin = async (code) => {
    try {
      isWxLoginLoading.value = true
      wxLoginError.value = ''
      
      const res = await request('/v1/wxMiniLogin', {
        method: 'POST',
        data: { code }
      })

      if (res.status === 200) {
        // 保存用户信息
        setToken(res.data.token)
        setUserInfo(res.data.account)
        setExpire(res.data.expire)
        setIsBind(!!res.data.token)
        if (res.data.merchants) {
          setMerchantList(res.data.merchants)
          if(res.data.merchants.length === 1){
            try{
              switchMerchant(res.data.merchants[0])
            }catch(error){
              toast(error.message ? error.message : `商户切换失败: 未知错误`, 'none')
              isWxLoginLoading.value = false
              return false;
            }
          }
        }

        // 登录成功，清除手动退出标志
        manuallyLoggedOut.value = false
        uni.setStorageSync('manuallyLoggedOut', false)
        
        isWxLoginLoading.value = false
        return true
      }
      
      wxLoginError.value = '登录失败，请稍后重试'
      isWxLoginLoading.value = false
      return false
    } catch (error) {
      console.error('微信静默登录失败:', error)
      wxLoginError.value = error.message || '登录失败，请稍后重试'
      isWxLoginLoading.value = false
      return false
    }
  }

  // 登出
  const logout = async () => {
    try {
      // 调用API登出接口
      await api.logout(token.value)

      // 清理本地状态
      userInfo.value = null
      merchantInfo.value = null
      merchantList.value = []
      token.value = ''
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('merchantInfo')
      uni.removeStorageSync('merchantList')
      uni.removeStorageSync('isBind')
      uni.removeStorageSync('manuallyLoggedOut')

      // 设置手动退出标志
      manuallyLoggedOut.value = true
      uni.setStorageSync('manuallyLoggedOut', true)

      // 清理历史会话数据
      const historyStore = useHistoryStore()
      historyStore.clearHistory()

      // 清理聊天数据
      const chatStore = useChatStore()
      chatStore.clearMessages()

      // 清理应用全局数据
      const appStore = useApplicationStore()
      appStore.clearAppData()

      return true
    } catch (error) {
      console.error('登出失败:', error)
      return false
    }
  }

  // 设置手动退出标志
  const setManuallyLoggedOut = (status) => {
    manuallyLoggedOut.value = status
    uni.setStorageSync('manuallyLoggedOut', status)
  }


  return {
    // 状态
    token,
    userInfo,
    merchantInfo,
    merchantList,
    currentMerchantObject,
    voicePlayEnabled,
    isBind,
    manuallyLoggedOut,
    isWxLoginLoading,
    wxLoginError,
    checkIsLoggedIn,
    // 方法
    initUserState,
    wxMiniLogin,
    setUserInfo,
    setToken,
    setMerchantInfo,
    setMerchantList,
    getMerchantList,
    getCurrentMerchant,
    switchMerchant,
    setAccountInfo,
    setExpire,
    logout,
    setVoicePlayEnabled,
    setIsBind,
    setManuallyLoggedOut
  }
})