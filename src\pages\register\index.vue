<script setup>
import { ref, reactive } from 'vue'
import { useUserStore } from '@/stores/user'



// 用户Store
const userStore = useUserStore()

// 表单数据
const registerForm = reactive({
  username: '',
  password: '',
  confirmPassword: ''
})

// 表单验证状态
const formErrors = reactive({
  username: '',
  password: '',
  confirmPassword: ''
})

// 注册状态
const isRegistering = ref(false)

// 表单验证
const validateForm = () => {
  let isValid = true
  
  // 清除之前的错误信息
  formErrors.username = ''
  formErrors.password = ''
  formErrors.confirmPassword = ''
  
  // 验证用户名
  if (!registerForm.username.trim()) {
    formErrors.username = '请输入用户名'
    isValid = false
  } else if (registerForm.username.length < 3) {
    formErrors.username = '用户名不能少于3个字符'
    isValid = false
  }
  
  // 验证密码
  if (!registerForm.password) {
    formErrors.password = '请输入密码'
    isValid = false
  } else if (registerForm.password.length < 6) {
    formErrors.password = '密码不能少于6个字符'
    isValid = false
  }
  
  // 验证确认密码
  if (!registerForm.confirmPassword) {
    formErrors.confirmPassword = '请确认密码'
    isValid = false
  } else if (registerForm.confirmPassword !== registerForm.password) {
    formErrors.confirmPassword = '两次输入的密码不一致'
    isValid = false
  }
  
  return isValid
}

// 注册
const handleRegister = async () => {
  if (!validateForm()) return
  
  try {
    isRegistering.value = true
    
    // 模拟API注册请求
    setTimeout(() => {
      // 模拟成功注册
      const userData = {
        id: 'user_' + Date.now(),
        username: registerForm.username,
        nickname: registerForm.username,
        avatar: ''
      }
      
      // 模拟token
      const token = 'mock_token_' + Date.now()
      
      // 保存用户信息和token
      userStore.setUserInfo(userData)
      userStore.setToken(token)
      
      // 提示成功
      uni.showToast({
        title: '注册成功',
        icon: 'success'
      })
      
      // 跳转回首页
      setTimeout(() => {
        uni.switchTab({
          url: '/pages/index/index'
        })
      }, 1500)
      
      isRegistering.value = false
    }, 1500)
  } catch (error) {
    console.error('注册失败:', error)
    uni.showToast({
      title: '注册失败，请重试',
      icon: 'none'
    })
    isRegistering.value = false
  }
}

// 返回登录页面
const goToLogin = () => {
  uni.navigateBack()
}
</script>

<template>
  <view class="register-container">
    <view class="register-header">
      <view class="back-button" @click="goToLogin">
        <view class="i-ic-baseline-arrow-back text-xl" />
      </view>
      <view class="header-title">注册</view>
    </view>
    
    <view class="register-content">
      <view class="app-logo">
        <image src="/static/logo.png" mode="aspectFit" class="logo-image" />
        <view class="app-name">AI 智能助手</view>
      </view>
      
      <view class="register-form">
        <view class="form-item">
          <BaseInput
            v-model="registerForm.username"
            placeholder="请输入用户名"
            :error="formErrors.username"
          />
        </view>
        
        <view class="form-item">
          <BaseInput
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码"
            :error="formErrors.password"
          />
        </view>
        
        <view class="form-item">
          <BaseInput
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请确认密码"
            :error="formErrors.confirmPassword"
          />
        </view>
        
        <view class="form-actions">
          <BaseButton
            @click="handleRegister"
            class="full-width-btn"
            :loading="isRegistering"
          >
            注册
          </BaseButton>
        </view>
        
        <view class="form-helper">
          <text class="login-link" @click="goToLogin">已有账号？立即登录</text>
        </view>
      </view>
    </view>
    
    <view class="register-footer">
      <text class="footer-text">注册代表您已同意《用户协议》和《隐私政策》</text>
    </view>
  </view>
</template>

<style>
.register-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
}

.register-header {
  position: relative;
  padding: 20px;
  display: flex;
  align-items: center;
}

.back-button {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-title {
  flex: 1;
  text-align: center;
  font-size: 18px;
  font-weight: 500;
}

.register-content {
  flex: 1;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.app-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40px;
}

.logo-image {
  width: 80px;
  height: 80px;
  margin-bottom: 10px;
}

.app-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.register-form {
  margin-bottom: 40px;
}

.form-item {
  margin-bottom: 20px;
}

.form-actions {
  margin-top: 30px;
}

.full-width-btn {
  width: 100%;
}

.form-helper {
  margin-top: 20px;
  text-align: center;
}

.login-link {
  color: #38C948;
  font-size: 14px;
}

.register-footer {
  padding: 20px;
  text-align: center;
}

.footer-text {
  font-size: 12px;
  color: #999;
}
</style> 