/*
Language: Python REPL
Requires: python.js
Author: <PERSON> <<EMAIL>>
Category: common
*/

export default function(hljs) {
  return {
    aliases: ['pycon'],
    contains: [
      {
        className: 'meta',
        starts: {
          // a space separates the REPL prefix from the actual code
          // this is purely for cleaner HTML output
          end: / |$/,
          starts: {
            end: '$', subLanguage: 'python'
          }
        },
        variants: [
          { begin: /^>>>(?=[ ]|$)/ },
          { begin: /^\.\.\.(?=[ ]|$)/ }
        ]
      },
    ]
  }
}
