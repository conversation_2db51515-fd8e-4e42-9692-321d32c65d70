/**
 * 将时间戳格式化为相对时间
 * 根据新的格式要求：
 * 1. 5分钟以内：显示“刚刚”
 * 2. 今日内：显示“时:分”
 * 3. 今日前但在今年内：显示“月-日 时:分”
 * 4. 今年前：显示“年-月-日 时:分”
 *
 * @param {number|string|Date} timestamp - 时间戳（毫秒）或日期对象或日期字符串
 * @returns {string} 格式化后的相对时间字符串
 */
export function formatRelativeTime(timestamp) {
  if (!timestamp) return '';

  // 将输入转换为Date对象
  const date = timestamp instanceof Date
    ? timestamp
    : new Date(typeof timestamp === 'number' ? timestamp : parseInt(timestamp));

  // 获取当前时间 - 每次调用时都重新获取当前时间
  const now = new Date();

  // 计算时间差（毫秒）
  const diff = now.getTime() - date.getTime();

  // 转换为秒和分钟
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);

  // 调试日志
  // console.log('格式化时间:', {
  //   timestamp: date.getTime(),
  //   now: now.getTime(),
  //   diff,
  //   minutes,
  //   seconds,
  //   dateStr: date.toLocaleString(),
  //   nowStr: now.toLocaleString()
  // });

  // 格式化小时和分钟
  const hours = String(date.getHours()).padStart(2, '0');
  const mins = String(date.getMinutes()).padStart(2, '0');
  const timeStr = `${hours}:${mins}`;

  // 格式化年月日
  const year = date.getFullYear().toString().slice(-2); // 取2位年份
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  // 判断是否是同一天
  const isSameDay = now.getDate() === date.getDate() &&
                    now.getMonth() === date.getMonth() &&
                    now.getFullYear() === date.getFullYear();

  // 判断是否是同一年
  const isSameYear = now.getFullYear() === date.getFullYear();

  // 根据时间差返回不同的格式
  // 确保时间差是正数，避免未来时间的问题
  if (diff < 0) {
    console.warn('时间戳在未来！', date.toLocaleString());
    return timeStr; // 如果时间戳在未来，直接返回时间
  }

  if (minutes < 2) {
    // 5分钟以内显示“刚刚”
    return '刚刚';
  } else if (isSameDay) {
    // 今日内显示“时:分”
    return timeStr;
  } else if (isSameYear) {
    // 今年内但不是今天显示“月-日 时:分”
    return `${month}-${day} ${timeStr}`;
  } else {
    // 不是今年显示“年-月-日 时:分”
    return `${year}-${month}-${day} ${timeStr}`;
  }
}

/**
 * 将时间戳格式化为标准时间格式（如“2023-01-01 12:34”）
 * @param {number|string|Date} timestamp - 时间戳（毫秒）或日期对象或日期字符串
 * @returns {string} 格式化后的时间字符串
 */
export function formatStandardTime(timestamp) {
  if (!timestamp) return '';

  // 将输入转换为Date对象
  const date = timestamp instanceof Date
    ? timestamp
    : new Date(typeof timestamp === 'number' ? timestamp : parseInt(timestamp));

  // 格式化年月日
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  // 格式化时分
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');

  // 返回格式化后的时间
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}
