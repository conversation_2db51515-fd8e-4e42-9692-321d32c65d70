# 历史聊天记录功能策划文档 (本地存储)

**1. 功能概述**

* **功能名称:** 历史聊天记录 (本地存储)
* **功能描述:**  在本地存储用户的聊天记录，方便用户查看之前的对话内容。**不涉及任何后端接口请求，所有数据均存储在本地设备。**
* **目标用户:** 所有使用 AI 问答小程序的用户。
* **核心价值:**
    * 允许用户回顾之前的对话，查找历史信息。
    * 提供更完整的对话体验，即使在关闭应用后也能保留对话上下文。
    * 完全本地存储，保护用户隐私，无需担心数据泄露风险。

**2. 产品文档参考**

* 参考 "md/产品文档.md" 文件中的以下部分：
    * 2. 功能列表 -> 辅助功能 (初期可考虑精简，后续迭代) -> 历史记录：查看用户的提问历史和 AI 回答。

**3. 技术方案**

* **前端技术栈:** UniApp (Vue3), Pinia,  `uni.storage` (本地存储)
* **状态管理:** 使用 Pinia 管理历史记录状态 (可选，如果需要在全局组件中访问历史记录)。
* **本地存储:** 使用 UniApp 提供的 `uni.storage` API 进行本地数据存储。
    * `uni.setStorageSync(key, data)`:  存储聊天记录数据。
    * `uni.getStorageSync(key)`:  读取聊天记录数据。
    * `uni.removeStorageSync(key)`:  删除聊天记录数据 (例如，清除缓存功能)。
    * `uni.clearStorage()`: 清空所有本地存储数据 (例如，清除缓存功能)。
* **数据结构:**
    * 聊天记录数据可以存储为 JSON 数组，每个元素代表一个会话。
    * 每个会话可以包含：
        * `sessionId`: 会话 ID (时间戳或 UUID)。
        * `messages`:  消息数组，包含用户消息和 AI 消息。
        * `timestamp`:  会话创建时间。

**4. 实现细节**

* **数据存储时机:**
    * 每当用户发送消息并收到 AI 回复后，将当前会话的完整消息列表更新到本地存储。
    * 在开启新会话时，也需要将之前的会话保存到本地存储。
* **数据读取时机:**
    * 在进入历史记录页面时，从本地存储读取所有会话列表。
    * (可选) 在首页加载时，可以读取最近的会话 (如果需要恢复上次会话)。
* **历史记录页面:**
    * 创建历史记录页面 (例如 `src/pages/history/index.vue`)，用于展示历史会话列表。
    * 列表项显示会话的简要信息 (例如，第一条用户消息内容，会话时间)。
    * 点击列表项进入会话详情页，展示完整的对话内容。
* **数据持久化:**
    * 聊天记录数据将持久化存储在本地设备中，除非用户手动清除缓存或卸载应用。

**5. UI/UX 设计**

* **入口:**
    * 在设置页或用户中心页添加 "历史记录" 入口。
    * (可选) 在首页顶部导航栏添加 "历史记录" 入口 (如果产品定位更侧重于历史记录的快速访问)。
* **历史记录列表页:**
    * 页面标题为 "历史记录"。
    * 列表按时间倒序排列，最近的会话显示在最前面。
    * 每个列表项简洁展示会话信息，例如：
        * 会话时间 (例如， "2023-10-27 14:30")。
        * 摘要信息 (例如，第一条用户消息或会话主题，限制字数)。
    * 列表项右侧可以添加 "进入会话" 箭头图标。
* **会话详情页:**
    * 页面标题为 "会话详情" 或显示会话时间。
    * 展示完整的对话消息列表，用户消息和 AI 消息区分显示，样式与首页对话页保持一致。
    * (可选) 允许用户复制、删除单条消息或整个会话。
* **空状态:**
    * 在没有历史记录时，历史记录列表页显示友好的空状态提示 (例如， "暂无历史记录")。

**6. 开发计划**

* **优先级:**  中期迭代功能 (初期可精简)
* **开发人员:**  [开发人员姓名]
* **预计时间:**  [预计开发时间]
* **测试计划:**  [测试计划]

**7.  待确认事项**

* 是否需要会话详情页？ 还是在历史记录列表页直接展示完整对话？
* 历史记录列表项需要展示哪些信息？ 摘要信息如何生成？
* 是否需要提供清除历史记录的功能？ (可以放在设置页的 "清除缓存" 中)

**8.  更新日志**

* [2024-03-21] v1.0.0 - 初始版本
    * 实现基本的本地存储功能
    * 创建历史记录列表页面
    * 实现会话详情页面
    * 添加消息保存和加载功能

* [2024-03-21] v1.1.0 - 优化历史记录功能
    * 将 TypeScript 改写为 JavaScript
    * 使用 setup 函数形式重构 store
    * 优化会话加载逻辑
    * 修复历史记录回显问题

**9. 遇到的问题及解决方案**

1. **问题：历史记录回显不完整**
   - 原因：在加载历史会话时，没有正确处理消息类型和内容
   - 解决：在 `index.vue` 的 `onMounted` 中添加了完整的消息加载逻辑，包括用户消息和 AI 回复

2. **问题：TypeScript 类型错误**
   - 原因：项目使用 JavaScript，但部分文件仍使用 TypeScript
   - 解决：将 `history.ts` 改写为 `history.js`，移除类型注解

3. **问题：Store 定义方式不统一**
   - 原因：混用了选项式 API 和组合式 API
   - 解决：统一使用 setup 函数形式定义 store，提高代码一致性

4. **问题：会话切换时状态混乱**
   - 原因：没有正确处理会话切换时的状态清理
   - 解决：在加载新会话前清空当前消息列表，确保状态正确

5. **问题：本地存储同步问题**
   - 原因：异步操作可能导致数据不同步
   - 解决：使用同步存储方法 `uni.setStorageSync` 和 `uni.getStorageSync`

**10. 后续优化计划**

1. **性能优化**
   - 实现历史记录分页加载
   - 优化大量消息时的渲染性能
   - 添加消息缓存机制

2. **功能增强**
   - 添加消息搜索功能
   - 支持消息导出
   - 添加消息分类和标签

3. **用户体验**
   - 优化加载状态显示
   - 添加消息编辑功能
   - 支持消息收藏

4. **数据安全**
   - 添加数据加密功能
   - 实现数据备份和恢复
   - 添加敏感信息过滤