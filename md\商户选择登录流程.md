# 商户选择后登录流程设计

## 1. 功能概述
当用户从uni-data-picker选择商户后，系统将调用`/v1/login`接口完成最终登录流程。

## 2. 完整流程说明

1. 用户输入账号密码点击登录
2. 调用`/v1/getMerchants`获取商户列表
3. 登录成功后弹出uni-data-picker选择商户
4. 用户选择商户后调用`/v1/login`完成最终登录
5. 保存商户信息并跳转首页

## 3. 接口规范

### 3.1 获取商户接口
```markdown
POST /v1/getMerchants
参数：
- account: 用户名
返回：
{
  "data": [
    {
      "merchant_type": 2,
      "merchant_id": 1,
      "label": "钓鱼岛加油站集团",
      "value": "2_1#0_0"
    }
  ]
}
```

### 3.2 最终登录接口
```markdown
POST /v1/login
参数：
- username: 用户名 (必填)
- password: 密码 (必填)
- remember: 是否自动登录 (0/1)
- merchant_type: 商户类型 (1=油站，2=集团)
- merchant_id: 商户ID
```

## 4. 流程设计
```mermaid
sequenceDiagram
    participant User as 用户
    participant Picker as uni-data-picker
    participant Login as 登录页面
    participant API as 后端接口
    participant Store as UserStore
    
    User->>Picker: 选择商户
    Picker->>Login: 触发@change事件
    Login->>API: POST /v1/login
    API-->>Login: 返回token和用户数据
    Login->>Store: 保存用户信息和商户数据
    Store-->>User: 跳转到首页
```

## 5. 核心代码实现

### 5.1 登录成功后处理
```javascript
// 在登录方法中添加商户数据处理
const handleLogin = async () => {
  // 原有登录逻辑...
  
  // 获取商户数据
  const merchantRes = await request('/v1/getMerchants', {
    method: 'POST',
    data: { account: loginForm.username }
  })
  
  // 显示商户选择弹窗
  if(merchantRes.data?.length > 0) {
    showMerchantPicker(merchantRes.data)
  } else {
    // 无商户直接跳转
    uni.switchTab({ url: '/pages/index/index' })
  }
}

// 显示商户选择弹窗
const showMerchantPicker = (merchants) => {
  merchantOptions.value = merchants.map(m => ({
    text: m.label,
    value: `${m.merchant_type}_${m.merchant_id}`
  }))
  merchantPicker.value.open()
}

### 5.2 商户选择处理
```javascript
// 在登录页面添加商户选择处理
const handleMerchantSelect = async (e) => {
  const selected = e.detail.value
  try {
    const res = await request('/v1/login', {
      method: 'POST',
      data: {
        username: loginForm.username,
        password: loginForm.password,
        merchant_type: selected.split('_')[0], // 从value提取类型
        merchant_id: selected.split('_')[1] // 从value提取ID
      }
    })
    
    // 保存用户信息和token
    userStore.setUserInfo(res.data.user)
    userStore.setToken(res.data.token)
    
    // 跳转首页
    uni.switchTab({ url: '/pages/index/index' })
  } catch (error) {
    uni.showToast({ title: '登录失败', icon: 'none' })
  }
}
```

## 6. 状态管理
需要在userStore中添加商户信息存储：
```javascript
// src/stores/user.js
const merchantInfo = ref(null)

const setMerchantInfo = (info) => {
  merchantInfo.value = info
  uni.setStorageSync('merchantInfo', info)
}
```

## 7. 错误处理方案
1. 网络错误：显示"网络连接失败"
2. 401错误：提示"商户选择无效"
3. 500错误：提示"系统繁忙，请重试"

## 8. 测试用例
| 测试场景 | 预期结果 |
|---------|---------|
| 选择有效商户 | 登录成功并跳转 |
| 选择无效商户 | 显示错误提示 |
| 网络中断 | 显示网络错误 |