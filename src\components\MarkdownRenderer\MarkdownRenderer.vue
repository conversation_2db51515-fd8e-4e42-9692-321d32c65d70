<script>
export default {
  options: {
    virtualHost: true,
    styleIsolation:'shared'
  },
  name: 'MarkdownRenderer',
}
</script>
<script setup>

// 移除 towxml
// const towxmlInstance = require('../../wxcomponents/towxml/index.js')

const emit = defineEmits(['suggested-question-selected'])

const props = defineProps({
  // markdown内容 - 现在期望是 towxml 解析后的对象，或者在流式传输时的原始字符串
  content: {
    type: [String, Object], // 允许字符串（流式）或对象（已解析）
    required: true
  },
  // 样式主题: light or dark - 不再需要，因为解析在外部完成
  // theme: {
  //   type: String,
  //   default: 'light'
  // }
})

// 移除解析后的内容 ref
// const parsedContent = ref(null)

// 移除解析 Markdown 的逻辑
// const parseMarkdown = () => { ... }

// 移除监听和初始解析
// watchEffect(() => { ... })
// onMounted(() => { ... })

// 处理 towxml 事件 (如果需要的话，可以保留或移到父级)
const handleTowxmlTap = (e) => {
  console.log("towxml tap event in MarkdownRenderer:", e);
  const { tag, attrs } = e.currentTarget.dataset.data || {};
  if (tag === 'navigator' && attrs.href) {
    if (attrs.href.startsWith('suggested://')) {
      const question = decodeURIComponent(attrs.href.replace('suggested://', ''));
      console.log('推荐问题点击 (MarkdownRenderer):', question);
      emit('suggested-question-selected', question);
    } else {
      // 普通链接已在 more-options 处理，这里可以不处理或作为备用
      // uni.setClipboardData({ ... });
    }
  }
};

// console.log('Markcontent',props.content)

</script>

<template>
  <view class="text-32rpx w-full rd overflow-hidden" v-show="content">
    <template v-if="typeof content === 'object' && content !== null">
      <!-- 直接使用 props.content 作为 towxml 的 nodes -->
      <towxml :nodes="content" class="leading-160%" @tap="handleTowxmlTap" />
    </template>
    <template v-else-if="typeof content === 'string'">
      <!-- 如果是字符串 (例如流式传输中或解析失败)，直接显示文本 -->
      <text user-select class="block p-3 leading-45rpx ">{{ content }}</text>
    </template>
    <!-- 可以添加一个加载或空状态 -->
    <!-- <template v-else>
      <text>...</text>
    </template> -->
  </view>
</template>