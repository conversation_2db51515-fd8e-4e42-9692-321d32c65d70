/*正文样式*/
.h2w {
    font-weight:400;
    font-size: 32rpx;
    line-height: 1.6;
    word-wrap: break-word;
    word-break: normal;
    text-align:justify;
}

.h2w__main {
    margin: 0 40rpx 40rpx 40rpx;
    padding-top: 40rpx;
}

/**标题**/
.h2w__h1,
.h2w__h2,
.h2w__h3,
.h2w__h4,
.h2w__h5,
.h2w__h6 {
    font-weight: bold;
}

/**设置行间元素样式**/
.h2w__span,
.h2w__b,
.h2w__strong,
.h2w__i,
.h2w__em,
.h2w__code,
.h2w__sub,
.h2w__sup,
.h2w__g-emoji,
.h2w__mark,
.h2w__u,
.h2w__navigatorParent,
.h2w__ins {
    display:inline;
}

.h2w__h1 {
    border-bottom-style: double;
    border-bottom-width: 6rpx;
    font-size: 42rpx;
    padding-bottom: 10rpx;
    margin-bottom: 20rpx;
}

.h2w__h2 {
    border-bottom-style: solid;
    border-bottom-width: 1rpx;
    font-size: 40rpx;
    padding-bottom: 8rpx;
    margin-bottom: 18rpx;
}

.h2w__h3 {
    font-size: 38rpx;
    padding-bottom: 6rpx;
    margin-bottom: 12rpx;
}

.h2w__h4 {
    font-size: 36rpx;
    padding-bottom: 4rpx;
    margin-bottom: 12rpx;
}

.h2w__h5 {
    font-size: 34rpx;
    padding-bottom: 2rpx;
    margin-bottom: 12rpx;
}

.h2w__h6 {
    margin-bottom: 12rpx;
}

/**组件父级容器**/
.h2w__textParent, .h2w__viewParent {
    display:inline;
}
.h2w__rich-textParent {
    overflow-x:auto;
}

/**表格**/
.h2w__tableParent {
    width:100%;
    overflow-x:auto;
}

.h2w__table {
    width: 99.99%;
    border-collapse: collapse;
    border-spacing: 0;
    display: table;
    margin-bottom: 40rpx;
    white-space: nowrap;
}

.h2w__table .h2w__tr:nth-child(2n) {
    background-color: red;
}

.h2w__colgroup {
    display: table-column-group;
}

.h2w__col {
    display: table-column;
}

.h2w__thead {
    display: table-header-group;
}

.h2w__tbody {
    display: table-row-group;
}

.h2w__tfoot {
    display: table-footer-group;
}

.h2w__tr {
    display: table-row;
}

.h2w__th,
.h2w__td {
    padding: 8rpx 16rpx;
    font-size: 28rpx;
    border-width: 1rpx;
    border-style: solid;
    display: table-cell;
}

.h2w__th {
    font-weight: bold;
}

/**代码块**/
.h2w__pre {
    /*white-space:nowrap;*/
    padding: 10rpx 14rpx 10rpx 10rpx;
    font-size: 28rpx;
    word-break: normal;
    border-width: 1rpx;
    border-style: solid;
    margin-bottom: 40rpx;
    white-space: nowrap;
    overflow-x: auto;
    tab-size:4;
}
.h2w__pre .h2w__p {
    margin:0;
}

.h2w__pre .h2w__code {
    padding: 0;
    border: 0;
    font-size: 100%;
}

.h2w__pre,
.h2w__code {
    font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace, "STHeitiTC-Light", "Microsoft YaHei Light", -apple-system, system-ui, BlinkMacSystemFont;
}

.h2w__code {
    padding: 4rpx 8rpx;
    margin: 0 4rpx;
    border-width: 1rpx;
    border-style: solid;
    border-radius: 8rpx;
    font-size: 80%;
    overflow-x: auto;
}

.h2w__pre .h2w__span,
.h2w__pre .h2w__a,
.h2w__pre .h2w__span,
.h2w__pre .h2w__b,
.h2w__pre .h2w__strong,
.h2w__pre .h2w__i,
.h2w__pre .h2w__em {
    display: inline;
}

.h2w__pre {
    white-space: pre;
    display: block;
}

.h2w__pre .h2w__code {
    white-space:nowrap;
    /* width: 9999px; */
    display: block;
    font-size: 80%;
}

/**列表**/
.h2w__ul,
.h2w__ol {
    margin-bottom: 40rpx;
    padding-left: 1rem;
}

.h2w__ul .h2w__ol,
.h2w__ol .h2w__ul {
    margin-bottom: 0;
}

.h2w__li {
    display: list-item;
}

/**todo**/
.h2w__todogroup {
    margin-bottom: 40rpx;
}

.h2w__todogroup .h2w__todogroup {
    padding-left: 1.6rem;
}

/**一级ol样式**/
.h2w__ol {
    list-style-type: decimal;
}

/**二级ol样式**/
.h2w__ul .h2w__ol,
.h2w__ol .h2w__ol {
    list-style-type: lower-roman;
}

/**三级ol样式**/
.h2w__ul .h2w__ul .h2w__ol,
.h2w__ul .h2w__ol .h2w__ol,
.h2w__ol .h2w__ul .h2w__ol,
.h2w__ol .h2w__ol .h2w__ol {
    list-style-type: lower-alpha;
}

/**一级ul样式**/
.h2w__ul {
    list-style-type: disc;
}

/**二级ul样式**/
.h2w__ul .h2w__ul,
.h2w__ol .h2w__ul {
    list-style-type: circle;
}

/**三级样式**/
.h2w__ol .h2w__ol .h2w__ul,
.h2w__ol .h2w__ul .h2w__ul,
.h2w__ul .h2w__ol .h2w__ul,
.h2w__ul .h2w__ul .h2w__ul {
    list-style-type: square;
}

/**块元素**/
.h2w__p {
    margin: 20rpx 0 20rpx 0;
}

.h2w__blockquote {
    border-left-width: 8rpx;
    border-left-style: solid;
    padding: 0 20rpx;
}

/**内连元素**/
.h2w__a,
.h2w__span,
.h2w__s,
.h2w__b,
.h2w__strong,
.h2w__i,
.h2w__em {
    display: inline;
}

.h2w__b,
.h2w__strong {
    font-weight: bold;
}

.h2w__i,
.h2w__em {
    font-style: italic;
}

/**文本删除线**/
.h2w__s,
.h2w__strike,
.h2w__del {
    text-decoration: line-through;
}

/**文本下划线**/
.h2w__ins,
.h2w__u {
    text-decoration: underline;
}

/**链接**/
.h2w__a {
    margin: 0 8rpx;
    border-bottom-width: 1rpx;
    border-bottom-style: solid;
    line-height: 1;
}

.h2w__hr {
    height: 8rpx;
    margin: 40rpx 0;
}

/**荧光标记**/
.h2w__mark {
    border-radius: 4rpx;
}

/**上标、下标**/
.h2w__sup,
.h2w__sub {
    font-size: 75%;
    position: relative;
}

.h2w__sup {
    top: -0.5em;
}

.h2w__sub {
    bottom: -0.25em;
}

/**emoji表情**/
.h2w__g-emoji {
    margin: 0 0.1em;
    font-family: "Apple Color Emoji", "Segoe UI", "Segoe UI Emoji", "Segoe UI Symbol";
}

/**内置元素**/
image,video {
    max-width: 100%;
}


video {
    width:100%; margin: 10rpx auto;
}

image {
    height:auto; vertical-align:middle;
}

video {
    height:220px; font-size:0;
}

.h2w__latex--line {margin:4rpx 8rpx; vertical-align:middle;}
.h2w__latex--block {display:block; margin:1em auto;}

.h2w__yuml {display:block;}

.h2w__yumlBox {
    width:100%;
    overflow-x:auto;
}
.h2w__yumlView {
    margin:0 auto; padding-bottom:40rpx;
}

/**代码行号**/
.h2w__lineNum {
    text-align:right; float:left; padding:0; margin:0 1em 0 0;
}
.h2w__lineNumLine {
    list-style:none;
}

.calltool {
    color: #333333;
    display: flex;
    line-height: 40rpx;
    align-items: flex-center;
    border-radius: 8rpx;
    padding: 4rpx 8rpx;
    margin-top: 10rpx;
    margin-bottom: 10rpx;
    position: relative;
    flex-direction: column;
  }

  .calltool::after {
    content: attr(data-error);
    display: none;
    font-size: 24rpx;
    color: #FF3B30;
    margin-top: 4rpx;
    margin-left: 24rpx;
    word-break: break-word;
    white-space: normal;
  }

  .calltool::before {
    content: attr(data-id);
    display: none;
    font-size: 20rpx;
    color: #999;
    position: absolute;
    right: 8rpx;
    top: 4rpx;
  }

  /* Show tool ID if available */
  .calltool[data-id]::before {
    display: block;
  }

  .calltool-error::after {
    display: block;
  }

  .calltool-complete {
    color: #38C948;
    border: 1px solid rgba(56, 199, 71, 0.4);
  }

  .calltool-stop {
    border: 1px solid rgba(255, 0, 0, 0.4);
  }

  .calltool-error {
    color: #FF3B30;
    border: 1px solid rgba(255, 59, 48, 0.4);
  }

  .calltool-start {
    border: 1px solid rgba(51, 51, 51, 0.4);
  }

  /* Keep backward compatibility with old class names */
  .calltool.complete {
    color: #38C948;
    border: 1px solid rgba(56, 199, 71, 0.4);
  }

  .calltool.stop {
    border: 1px solid rgba(255, 0, 0, 0.4);
  }

  .calltool.error {
    color: #FF3B30;
    border: 1px solid rgba(255, 59, 48, 0.4);
  }

  .calltool.error::after {
    display: block;
  }

  .calltool.start {
    border: 1px solid rgba(51, 51, 51, 0.4);
  }

  .calltool .icon {
    font-size: 40rpx;
    display: inline-block;
    vertical-align: middle;
    mask-size: 100% 100%;
    background-color: currentColor;
    color: inherit;
    width: 1em;
    height: 1em;
    mask: var(--un-icon) no-repeat;
    -webkit-mask: var(--un-icon) no-repeat;
    -webkit-mask-size: 100% 100%;
    margin-right: 4rpx;
    position:relative;
    top:-4rpx;
  }

  .calltool.complete .icon, .calltool-complete .icon {
    --un-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3C!-- Icon from Material Line Icons by Vjacheslav Trushkin - https://github.com/cyberalien/line-md/blob/master/license.txt --%3E%3Cg fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath stroke-dasharray='64' stroke-dashoffset='64' d='M3 12c0 -4.97 4.03 -9 9 -9c4.97 0 9 4.03 9 9c0 4.97 -4.03 9 -9 9c-4.97 0 -9 -4.03 -9 -9Z'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' dur='0.6s' values='64;0'/%3E%3C/path%3E%3Cpath stroke-dasharray='14' stroke-dashoffset='14' d='M8 12l3 3l5 -5'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' begin='0.6s' dur='0.2s' values='14;0'/%3E%3C/path%3E%3C/g%3E%3C/svg%3E");
  }

  .calltool.stop .icon, .calltool-stop .icon {
    --un-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3C!-- Icon from Material Line Icons by Vjacheslav Trushkin - https://github.com/cyberalien/line-md/blob/master/license.txt --%3E%3Cg fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath stroke-dasharray='64' stroke-dashoffset='64' d='M12 3c4.97 0 9 4.03 9 9c0 4.97 -4.03 9 -9 9c-4.97 0 -9 -4.03 -9 -9c0 -4.97 4.03 -9 9 -9Z'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' dur='0.6s' values='64;0'/%3E%3C/path%3E%3Cpath stroke-dasharray='8' stroke-dashoffset='8' d='M12 12l4 4M12 12l-4 -4M12 12l-4 4M12 12l4 -4'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' begin='0.6s' dur='0.2s' values='8;0'/%3E%3C/path%3E%3C/g%3E%3C/svg%3E");
    color: red;
  }

  .calltool.error .icon, .calltool-error .icon {
    --un-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3C!-- Icon from Material Line Icons by Vjacheslav Trushkin - https://github.com/cyberalien/line-md/blob/master/license.txt --%3E%3Cg fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath stroke-dasharray='64' stroke-dashoffset='64' d='M12 3c4.97 0 9 4.03 9 9c0 4.97 -4.03 9 -9 9c-4.97 0 -9 -4.03 -9 -9c0 -4.97 4.03 -9 9 -9Z'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' dur='0.6s' values='64;0'/%3E%3C/path%3E%3Cpath stroke-dasharray='16' stroke-dashoffset='16' d='M12 8v5'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' begin='0.7s' dur='0.2s' values='16;0'/%3E%3C/path%3E%3Ccircle cx='12' cy='16' r='1' fill='currentColor' fill-opacity='0'%3E%3Canimate fill='freeze' attributeName='fill-opacity' begin='0.9s' dur='0.2s' values='0;1'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");
    color: #FF3B30;
  }

  .calltool.start .icon, .calltool-start .icon {
    --un-icon: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3C!-- Icon from Material Line Icons by Vjacheslav Trushkin - https://github.com/cyberalien/line-md/blob/master/license.txt --%3E%3Cg fill='none' stroke='currentColor' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath stroke-dasharray='16' stroke-dashoffset='16' d='M12 3c4.97 0 9 4.03 9 9'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' dur='0.3s' values='16;0'/%3E%3CanimateTransform attributeName='transform' dur='1.5s' repeatCount='indefinite' type='rotate' values='0 12 12;360 12 12'/%3E%3C/path%3E%3Cpath stroke-dasharray='64' stroke-dashoffset='64' stroke-opacity='.3' d='M12 3c4.97 0 9 4.03 9 9c0 4.97 -4.03 9 -9 9c-4.97 0 -9 -4.03 -9 -9c0 -4.97 4.03 -9 9 -9Z'%3E%3Canimate fill='freeze' attributeName='stroke-dashoffset' dur='1.2s' values='64;0'/%3E%3C/path%3E%3C/g%3E%3C/svg%3E");
  }