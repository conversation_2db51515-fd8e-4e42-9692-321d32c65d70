<script setup lang="ts">
const version = '1.0.0'
</script>

<template>
  <view class="min-h-screen bg-gray-50">
    <!-- Logo -->
    <view class="flex flex-col items-center py-12">
      <image
        src="/static/logo.png"
        mode="aspectFit"
        class="w-24 h-24 rounded-full mb-4"
      />
      <text class="text-xl font-medium text-gray-800">AI 智能问答助手</text>
      <text class="text-sm text-gray-500 mt-2">版本 {{ version }}</text>
    </view>

    <!-- 功能介绍 -->
    <view class="bg-white px-4 py-6">
      <text class="text-base text-gray-800 font-medium mb-4 block">功能介绍</text>
      <view class="space-y-4">
        <view class="flex items-start">
          <text class="i-ic-carbon-chat-bot text-xl text-primary mr-3 mt-1"></text>
          <view>
            <text class="text-base text-gray-800 block">智能问答</text>
            <text class="text-sm text-gray-500 mt-1 block">基于大语言模型，为您提供准确、深入的答案</text>
          </view>
        </view>
        <view class="flex items-start">
          <text class="i-ic-carbon-image text-xl text-primary mr-3 mt-1"></text>
          <view>
            <text class="text-base text-gray-800 block">多模态输入</text>
            <text class="text-sm text-gray-500 mt-1 block">支持文字、图片、语音、文件等多种输入方式</text>
          </view>
        </view>
        <view class="flex items-start">
          <text class="i-ic-carbon-time text-xl text-primary mr-3 mt-1"></text>
          <view>
            <text class="text-base text-gray-800 block">历史记录</text>
            <text class="text-sm text-gray-500 mt-1 block">保存您的对话历史，随时查看和回顾</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 联系方式 -->
    <view class="bg-white px-4 py-6 mt-4">
      <text class="text-base text-gray-800 font-medium mb-4 block">联系我们</text>
      <view class="space-y-4">
        <view class="flex items-center">
          <text class="i-ic-carbon-email text-xl text-primary mr-3"></text>
          <text class="text-base text-gray-800"><EMAIL></text>
        </view>
        <view class="flex items-center">
          <text class="i-ic-carbon-phone text-xl text-primary mr-3"></text>
          <text class="text-base text-gray-800">************</text>
        </view>
      </view>
    </view>

    <!-- 版权信息 -->
    <view class="text-center py-8">
      <text class="text-sm text-gray-500">Copyright © 2024 AI 智能问答助手</text>
      <text class="text-sm text-gray-500 block mt-2">All Rights Reserved</text>
    </view>
  </view>
</template> 