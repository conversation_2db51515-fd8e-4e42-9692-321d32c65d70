import { useUserStore } from '@/stores/user'
import { useChatStore } from '@/stores/chat'
import { getConfig } from '@/config'

// API配置
export const API_CONFIG = {
  BASE_URL: getConfig().BASE_URL,
  DEFAULT_PARAMS: {
    response_mode: 'streaming'
  }
}


// 请求拦截器函数
export const request = async (url, options = {}) => {
  try {
    const userStore = useUserStore()
    const userToken = userStore.token
    const isLoggedIn = userStore.checkIsLoggedIn()

    // 处理user参数 - 跳过/v1/login接口
    if (!url.includes('/v1/login')) {
      const userValue = userStore.userInfo?.username || ''

      // 处理params中的user
      if (options.params) {
        options.params.user = userValue
      } else {
        options.params = { user: userValue }
      }

      // 处理data中的user
      if (options.data && typeof options.data === 'object') {
        options.data.user = userValue
      }
    }

    const fullUrl = url.startsWith('http://') || url.startsWith('https://')
      ? url
      : `${API_CONFIG.BASE_URL}${url.startsWith('/') ? url : `/${url}`}`
    console.log(`Requesting: ${fullUrl}`, options)

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json',
      ...options.header
    }
    // 仅当用户已登录时添加Authorization头
    if (isLoggedIn && userToken) {
      headers['Authorization'] = `Bearer ${userToken}`
    }

    // 检查特定接口的登录状态要求
    const protectedRoutes = ['/v1/conversations']
    if (protectedRoutes.some(route => url.includes(route))) {
      if (!isLoggedIn) {
        throw new Error('请先登录后再访问此功能')
      }
    }

    // 发起请求
    const res = await uni.request({
      enableChunked: !!options.enableChunked,
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data,
      responseType: options.responseType,
      header: headers
    })

    // uni.request在不同平台返回格式不同，需要判断
    // const [error, res] = Array.isArray(response) ? response : [null, response]
    console.log('Response status:', res)

    // 处理错误状态码
    // if (res.statusCode === 401) {
    //   // 未授权，可能是token过期
    //   if (isLoggedIn) {
    //     console.warn('Token可能已过期，尝试重新获取')
    //     userStore.logout()
    //     const chatStore = useChatStore()
    //     chatStore.clearMessages()

    //     uni.showToast({
    //       title: '登录已过期，请重新登录',
    //       icon: 'none'
    //     })
    //   }
    //   throw new Error(res.data?.msg || '认证失败，请重新登录')
    // }

    if (res.statusCode === 403) {
      // 禁止访问，不退出登录
      throw new Error(res.data?.msg || '无权限访问此资源')
    }

    if (res.statusCode < 200 || res.statusCode >= 300) {
      console.error('HTTP错误:', res.statusCode, res.data)
      throw new Error(res.data?.msg || `请求错误: ${res.statusCode}`)
    }
    const aiURL = ['/v1/chat-message', '/v1/conversations',]
    for(const urlItem of aiURL){
      if (url.includes(urlItem)) {
        return res;
      }
    }

    // 检查响应数据中的状态码
    if (res.data?.status && res.data.status !== 200) {
      console.error('业务错误:', res.data.status, res.data)
      throw new Error(res.data?.msg || `业务错误: ${res.data.status}`)
    }

    return res.data
  } catch (error) {
    console.error('API请求错误:', error)
    throw error
  }
}

// 解析响应数据
export const parseResponse = (data) => {
  try {
    // 提取消息内容
    if (data && data.message && data.message.content) {
      return {
        content: data.message.content,
        messageId: data.message.id,
        parent_message_id: data.parent_message_id,
        conversation_id: data.conversation_id
      }
    }

    // 如果没有标准的消息格式，尝试直接返回数据
    if (data && typeof data === 'object') {
      return {
        content: data.content || data.answer || JSON.stringify(data),
        messageId: data.id || data.message_id || '',
        parent_message_id: data.parent_message_id || '',
        conversation_id: data.conversation_id || ''
      }
    }

    return null
  } catch (error) {
    console.error('解析响应数据失败:', error)
    return null
  }
}