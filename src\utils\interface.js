/**
 * 显示一个弹出框，用于提示用户一些信息。
 * @function
 * @param {string} content - 弹出框显示的内容。
 * @param {Object} [config] - 弹出框的配置选项。
 * @param {string} [config.icon='none'] - 弹出框的图标类型，可选值包括：success（成功）、loading（加载中）、none（无图标）。
 * @param {string} [config.title=content] - 弹出框的提示文字。如果未指定，则默认使用 content 参数的值。
 * @param {boolean} [config.mask=true] - 是否显示一个半透明的蒙层，防止用户在弹出框显示时操作页面。
 * @example
 * toast('Hello, World!'); // 显示一个没有图标的弹出框，其内容为 'Hello, World!'
 * toast('操作成功', { icon: 'success' }); // 显示一个带有成功图标的弹出框，其内容为 '操作成功'
 */
export function toast(content, config = {}) {
  uni.hideLoading();

  uni.showToast({
    icon: 'none',
    duration: 2000,
    ...{mask: false, ...config},
    title: content
  });
}

/**
 * 显示加载中的提示框
 * @param {string} content - 提示框的内容
 * @param {string} [title] - 提示框的标题
 * @param {Object} [config={}] - 配置对象，可选
 * @param {boolean} [config.mask=true] - 是否显示蒙层，默认为 true
 * @param {string} [config.image] - 自定义图标的本地路径，image 的优先级高于 icon
 * @param {string} [config.icon] - 图标类型，有效值为 'success'、'loading'、'none'，默认为 'none'
 * @param {number} [config.duration] - 提示框的持续时间（单位：毫秒），超过该时间后自动关闭提示框，默认为 2000
 * @returns {void}
 */
export function loading(title = '', config = {}) {
  uni.showLoading({
    ...{mask: false, ...config},
    title
  });
}

/**
 * 隐藏加载指示器。
 *
 * @function
 * @name hideLoading
 * @returns {void}
 */
export function hideLoading() {
  uni.hideLoading();
}

/**
 * 打开地图选择位置界面，获取用户选择的位置信息。
 *
 * @param {Object} config - 打开地图选择位置界面的配置选项。
 * @param {string} config.latitude - 纬度，范围为 -90~90，负数表示南纬。
 * @param {string} config.longitude - 经度，范围为 -180~180，负数表示西经。
 * @param {string} [config.name] - 位置名。
 * @param {string} [config.address] - 地址的详细说明。
 * @param {string} [config.scale] - 缩放比例，范围 5~18。
 * @param {string} [config.markerColor] - 标注的颜色，有效值为：red、green、blue。
 * @param {Function} [config.success] - 用户成功选择位置的回调函数。
 * @param {Function} [config.fail] - 用户取消选择位置或发生错误时的回调函数。
 * @param {Function} [config.complete] - API 调用结束时的回调函数（调用成功、失败都会执行）。
 * @returns {Promise} 返回 Promise 对象，可以通过 then 和 catch 方法来处理回调结果。
 */
export function openLocation(config) {
  // 调用 uni.openLocation 方法，返回 Promise 对象。
  return uni.openLocation(config);
}
