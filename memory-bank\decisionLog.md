[2025-04-08 09:26:00] - 重构微信小程序静默登录实现
- 从使用api.wxMiniLogin改为直接使用request函数
- 保持相同功能但实现更直接
- 确保请求参数和响应处理一致
- 提升代码可读性和维护性

[2025-04-07 12:03:00] - 重构商户选择组件
- 支持value传入商户ID或完整对象两种模式
- 使用@select事件替代@update:value传递完整商户对象
- 确保与useMerchantSelect兼容
- 保持v-model双向绑定功能
[2025-04-07 10:44:00] - 移除菜单项hover样式
- 删除hover:bg-gray-100类
- 移除transition-colors duration-100过渡效果
- 保持原有功能不变

[2025-04-07 10:37:00] - 修改用户退出登录后的返回逻辑
- 退出后直接返回首页
- 保持原有登出功能不变
- 优化用户体验
[2025-04-03 10:52:40] - 技术决策：开场白功能实现方案
- 完全复用现有parameters数据结构
- 通过标准fetchParameters()方法获取数据
- 直接使用Store中的parameters数据
- 遵循文档中的异常处理规范
[2025-04-02 17:03:00] - 优化历史记录分页加载逻辑：
- 在history store中添加hasMore状态
- 更新loadConversations方法存储has_more
- 确保hasMore在store中可访问
- 保持分页逻辑一致
- 解决滚动到底部内容不可见问题

[2025-04-02 16:23:48] - 完善HistoryDrawer组件边框样式：
- 为所有border添加border-solid样式
- 统一border颜色透明度为80%
- 确保样式三要素完整
[2025-04-02 16:21:00] - 移除了HistoryDrawer组件中的标题和关闭按钮，改用CSS变量--status-bar-height控制顶部间距
[2025-04-02 16:20:00] - HistoryDrawer组件UI优化
- 移除历史记录标题和关闭图标
- 使用--status-bar-height CSS变量调整顶部间距
- 保持原有功能不变
- 提升视觉简洁度

[2025-03-31 21:54:00] - 分离403和401状态码处理逻辑
- 403状态码不再触发登出操作
- 403状态码显示"未开通智能体"提示
- 401状态码仍保持原有登出逻辑
- 确保权限不足和认证失败有明确区分

[2025-03-31 11:49:00] - 优化登录状态校验机制
- 完善登录页面(index.vue)的校验流程
- 强化用户状态管理(user.js)的可靠性
- 确保所有受保护路由都经过统一校验
- 保持与后端认证机制的一致性

[2025-03-31 11:41:00] - 移除前端会话保存功能
- 消息记录改为完全通过后端接口获取
- 移除saveCurrentSession相关调用
- 确保历史记录抽屉仍能正常工作
- 需要后端保证消息持久化可靠性

[2025-03-29 04:03:00] - 临时隐藏长按复制功能
- 注释了MessageBubble.vue中的handleLongPress函数
- 移除了@longpress事件绑定
- 保留copyContent函数定义以便后续恢复
- 确保不影响其他消息交互功能
- 需要后续评估是否完全移除该功能

[2025-03-29 03:58:00] - 临时禁用表格转echarts功能
- 注释了所有parseMarkdownTable调用点
- 保留函数定义以便后续恢复
- 确保不影响其他功能正常运行
- 需要后续评估是否完全移除该功能

[2025-03-29 03:43:00] - 实现v1/conversations接口登录校验
- 在request.js中添加protectedRoutes数组管理受保护路由
- 请求前检查用户登录状态
- 未登录时抛出错误提示
- 保持原有功能兼容性

[2025-03-29 03:40:04] - 统一导航跳转处理规范
- 所有页面跳转必须使用navigate工具函数
- 禁止直接使用uni.navigateTo等原生API
- 确保登录状态校验统一处理
- 保持原有功能兼容性

[2025-03-29 02:38:22] - 优化会话列表加载策略
- 统一会话列表加载入口到App.vue
- 禁止组件重复初始化加载
- 保留滚动加载更多功能
- 减少不必要的API调用
[2025-03-29 02:11:00] - 在request.js中实现登录用户请求自动添加Authorization头功能
- 移除了默认API KEY逻辑
- 修改headers构造逻辑，仅当用户登录时添加Authorization头
- 启用401/403状态码处理逻辑
- 保持原有功能兼容性
[2025-03-27 21:38:00] - 统一user参数处理优化
- 移除history.js和api.js中手动设置的user参数
- 完全依赖http.js拦截器的自动处理
- 确保所有接口调用遵循统一规范

[2025-03-29 00:40:01] - 函数导入规范
- 所有外部使用的函数都必须显式导入
- 包括Vue API、工具函数、第三方库等
- 代码审查时必须检查：
  * 所有使用到的函数是否都有对应import
  * import路径是否正确
  * 避免依赖全局变量
- 建议使用ESLint的no-undef规则进行检查

[2025-04-01 16:39:00] - 重构组件导入方式
- 移除MessageBubble和BaseButton的显式导入
- 改为使用easycom自动导入机制
- 保持所有组件标签不变

| 2025-04-03 10:28:00 | 使用现有parameters变量存储opening_statement | 保持代码简洁，避免冗余状态 | 直接通过parameters.opening_statement访问开场白 |
| 2025-04-03 10:34:00 | 更新开场白文档 | 根据反馈调整空值处理和实现方案 | 移除默认值，使用现有字段 |
| 2025-04-03 10:41:00 | 最终调整开场白文档 | 优化获取逻辑和缓存策略 | 文档版本v1.3 |
| 2025-04-03 10:43:00 | 最终优化开场白文档 | 移除watch使用建议 | 文档版本v1.4 |
[2025-04-05 13:11:30] - 修改iPhone X安全区域处理方式：从使用CSS env()函数改为pb-safe类，以解决iPhone X类手机的覆盖问题
| 日期时间 | 决策内容 | 原因 | 影响范围 |
|----------|----------|------|----------|
| 2025-04-07 15:50:00 | 优化商户切换逻辑 | 解决状态不一致问题 | useMerchantSelect.js |
| 2025-04-07 15:50:00 | 统一提示使用toast函数 | 保持提示一致性 | 多个文件 |
| 2025-04-07 15:50:00 | 修改登录接口参数处理 | 避免多余参数 | request.js |

| 2024-04-07 16:08:32 | 在more-options组件中添加会话进行中检查逻辑 | 保持与index页面相同的用户体验和逻辑一致性 | 修改了src/components/more-options/more-options.vue中的handleSessionClick函数，添加了与src/pages/index/index.vue中handleMenuItemClick相同的检查逻辑 |

[2025-04-07 19:34:00] - 微信绑定状态同步架构决策
* 决策：在绑定/解绑成功后同步状态到UMB系统
* 理由：保持系统状态一致性，便于追踪用户绑定状态
* 实现细节：
  - 在src/pages/user/index.vue中添加UMB同步调用
  - 使用try-catch处理UMB同步错误
  - 确保不影响主流程的正常运行
  - 保持现有UI风格和提示方式
- 确保功能完全兼容
- 需要验证所有使用场景正常工作