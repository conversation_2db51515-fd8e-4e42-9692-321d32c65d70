import { computed, ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { request } from '@/services/request'
import { toast, loading, hideLoading } from '@/utils/interface'

export default function useWechatBinding() {
  const userStore = useUserStore()
  const isWechatBound = computed(() => userStore.isBind)
  const isLoading = ref(false)

  const handleGetPhoneNumber = async (e) => {
    if (isLoading.value) return
    isLoading.value = true

    try {
      if (e.errMsg !== 'getPhoneNumber:ok') {
        throw new Error('用户拒绝授权手机号')
      }

      const { encryptedData, iv } = e
      if (!encryptedData || !iv) {
        throw new Error('获取微信授权信息失败')
      }

      const loginRes = await new Promise((resolve, reject) => {
        uni.login({
          provider: 'weixin',
          success: resolve,
          fail: reject
        })
      })

      loading('绑定中...')

      const res = await request('/v1/wxMiniBind', {
        method: 'POST',
        data: {
          code: loginRes.code,
          encryptedData,
          iv
        }
      })

      if (res.status !== 200) {
        throw new Error(res?.msg || '绑定失败，请重试')
      }

      userStore.setIsBind(true)
      toast('微信绑定成功', 'success')
    } catch (error) {
      console.error('微信绑定失败:', error)
      toast(error.message.includes('login') ? '获取微信登录code失败' : error.message || '绑定失败，请重试')
    } finally {
      isLoading.value = false
      hideLoading()
    }
  }

  const handleWechatUnbindClick = async () => {
    if (!isWechatBound.value || isLoading.value) return

    uni.showModal({
      title: '解绑微信',
      content: '确定要解除微信绑定吗？',
      success: async (res) => {
        if (res.confirm) {
          isLoading.value = true
          try {
            const loginRes = await new Promise((resolve, reject) => {
              uni.login({
                provider: 'weixin',
                success: resolve,
                fail: reject
              })
            })

            loading('解绑中...')

            const res = await request('/v1/wxMiniUntie', {
              method: 'POST',
              data: {
                code: loginRes.code
              }
            })

            if (res.status !== 200) {
              throw new Error(res?.msg || '解绑失败，请重试')
            }

            userStore.setIsBind(false)
            toast('微信解绑成功', 'success')
          } catch (error) {
            console.error('微信解绑失败:', error)
            toast(error.message.includes('login') ? '获取微信登录code失败' : error.message || '解绑失败，请重试')
          } finally {
            isLoading.value = false
            hideLoading()
          }
        }
      }
    })
  }

  const setDismissBindingPrompt = (dismissed) => {
    try {
      uni.setStorageSync('wechat_binding_prompt_dismissed', dismissed)
    } catch (error) {
      console.error('保存微信绑定提示关闭状态失败:', error)
    }
  }

  const getDismissBindingPrompt = () => {
    try {
      return uni.getStorageSync('wechat_binding_prompt_dismissed') || false
    } catch (error) {
      console.error('获取微信绑定提示关闭状态失败:', error)
      return false
    }
  }

  return {
    isWechatBound,
    handleGetPhoneNumber,
    handleWechatUnbindClick,
    setDismissBindingPrompt,
    getDismissBindingPrompt
  }
}