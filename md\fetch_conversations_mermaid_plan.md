# 功能策划：点击历史记录回显会话 (Mermaid 版)

## 1. 目标

当用户在历史记录列表（例如 `HistoryDrawer.vue` 侧边栏）中点击某个会话项时，能够调用 API 获取该会话的所有消息记录，并在主聊天界面 (`index.vue`) 中显示这些记录。

## 2. API 信息

-   **Endpoint:** `GET http://192.168.85.184:3001/v1/messages`
-   **Query Parameters:**
    -   `user`: 用户标识符 (必填)
    -   `conversation_id`: 会话 ID (必填)
    -   `last_id`: (可选) 用于分页，获取此 ID 之前的消息
    -   `limit`: (可选) 每次获取的消息数量，默认为 20
-   **Headers:**
    -   `Authorization: Bearer {api_key}` (由 `request` 函数自动处理)
-   **Success Response (Example):**
    ```json
    {
      "limit": 20,
      "has_more": false,
      "data": [
        {
          "id": "msg-xxx",
          "conversation_id": "conv-yyy",
          "inputs": { ... },
          "query": "User's question",
          "answer": "AI's answer",
          "message_files": [],
          "feedback": null,
          "retriever_resources": [ ... ],
          "created_at": 1705569239 // 秒级时间戳
        },
        // ... more messages (按 created_at 降序排列)
      ]
    }
    ```

## 3. 核心流程 (Mermaid Sequence Diagram)
