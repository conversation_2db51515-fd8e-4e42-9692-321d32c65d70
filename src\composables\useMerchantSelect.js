import { computed, ref } from 'vue'
import { useUserStore } from '@/stores/user'
import { toast } from '@/utils/interface'

export default function useMerchantSelect() {
  const userStore = useUserStore()

  const merchants = computed(() => userStore.merchantList || [])
  const currentMerchant = computed(() => userStore.merchantInfo?.merchant_id || null)

  const formattedMerchants = computed(() => {
    return merchants.value.map(item => ({
      text: item.label || item.merchantName,
      value: item.merchant_id || item.id
    }))
  })

  const loading = ref(false)

  const handleMerchantChange = async (merchantId) => {
    const merchant = merchants.value.find(m => m.merchant_id === merchantId)
    if (!merchant) {
      toast('未找到该商户信息', 'none')
      return
    }

    loading.value = true
    try {
      const success = await userStore.switchMerchant({
        merchant_id: merchantId,
        merchant_type: merchant.merchant_type || 1
      })

      if (success) {
        // API调用成功后再更新状态
        userStore.setMerchantInfo(merchant)
        toast('商户切换成功', 'success')
      } else {
        toast('商户切换失败，请稍后重试', 'none')
      }
    } catch (error) {
      console.error('商户切换错误:', error.message)
      toast(error.message ? error.message : `商户切换失败: 未知错误`, 'none')
    } finally {
      loading.value = false
    }
  }

  return {
    merchants,
    currentMerchant,
    formattedMerchants,
    handleMerchantChange
  }
}