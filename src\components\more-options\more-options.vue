<script setup>
import { toast } from '@/utils/interface';
import { navigate } from '@/utils/navigate';
import { ref, onMounted, watch, computed, nextTick } from 'vue';
import { useHistoryStore } from '@/stores/history';
import { useUserStore } from '@/stores/user';
import { useChatStore } from '@/stores/chat';
import { storeToRefs } from 'pinia';

// 胶囊按钮信息
const capsuleInfo = ref({
  height: 32, // 默认高度
  top: 0,    // 默认顶部位置
  bottom: 0  // 默认底部位置
});

// 获取胶囊按钮信息
const getCapsuleInfo = () => {
  try {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    if (menuButtonInfo) {
      // 更新胶囊按钮信息
      capsuleInfo.value = {
        height: menuButtonInfo.height,
        top: menuButtonInfo.top,
        bottom: menuButtonInfo.bottom
      };
      console.log('胶囊按钮信息:', capsuleInfo.value);
    }
  } catch (error) {
    console.error('获取胶囊按钮信息失败:', error);
  }
};

// 移除 visible 属性
const props = defineProps({});

// 修改 emit 事件，添加 reset-conversation 和 reset-scroll 事件
const emit = defineEmits(['close', 'select-session', 'reset-conversation', 'reset-scroll']);

// 定义关闭方法
const handleClose = () => {
  // 关闭上下文菜单
  showContextMenu.value = false;
  // 触发关闭事件
  emit('close');
};

// 当前要删除的会话
const sessionToDelete = ref(null);
// 当前要编辑的会话
const sessionToEdit = ref(null);
// 编辑输入框的内容
const editNameInput = ref('');
// 编辑弹窗引用
const editPopupRef = ref(null);
// 添加输入框聚焦状态
const inputFocus = ref(false);

// 长按菜单相关
const showContextMenu = ref(false);
const contextMenuPosition = ref({ x: 0, y: 0 });
const contextMenuSession = ref(null);

// 显示删除确认对话框
const showDeleteConfirm = () => {
  uni.showModal({
    title: '确认删除',
    content: '该对话内容将被删除无法恢复',
    success: async (res) => {
      if (res.confirm && sessionToDelete.value) {
        try {
          // 检查是否是当前会话
          const isCurrentSession = sessionToDelete.value.sessionId === historyStore.currentSession?.sessionId;

          // 删除会话
          await historyStore.deleteSession(sessionToDelete.value.sessionId);
          toast('删除成功');

          // 如果删除的是当前会话，清空聊天记录并创建新会话
          if (isCurrentSession) {
            // 获取chatStore实例
            const chatStore = useChatStore();
            // 清空消息
            chatStore.clearMessages();
            // 创建新会话
            historyStore.createNewSession();
            // 通知父组件重置当前会话ID
            emit('reset-conversation');
          }
        } catch (error) {
          console.error('删除失败:', error);
          toast('删除失败，请重试');
        } finally {
          sessionToDelete.value = null;
        }
      } else {
        sessionToDelete.value = null;
      }
    }
  });
};

// 处理删除按钮点击
const handleDeleteClick = (session, event) => {
  // 阻止事件冒泡，防止触发会话点击
  if (event) event.stopPropagation();
  sessionToDelete.value = session;
  showDeleteConfirm();
  // 关闭上下文菜单
  showContextMenu.value = false;
};

// 处理编辑按钮点击
const handleEditClick = (session, event) => {
  // 阻止事件冒泡，防止触发会话点击
  if (event) event.stopPropagation();
  sessionToEdit.value = session;
  editNameInput.value = session.name;

  // 打开编辑弹窗
  editPopupRef.value.open('center');
  // 关闭上下文菜单
  showContextMenu.value = false;
};

// 处理长按事件
const handleLongPress = (session, event) => {
  // 阻止事件冒泡，防止触发会话点击
  event.preventDefault();
  event.stopPropagation();

  // 如果对话正在进行中，不显示菜单
  const chatStore = useChatStore();
  const { isLoading: isChatLoading } = storeToRefs(chatStore);

  if (isChatLoading.value) {
    toast('对话进行中，请稍后…');
    return;
  }

  // 获取触摸位置
  const touchX = event.touches[0].clientX;
  const touchY = event.touches[0].clientY;

  // 获取屏幕尺寸
  const systemInfo = uni.getSystemInfoSync();
  const screenWidth = systemInfo.windowWidth;
  const screenHeight = systemInfo.windowHeight;

  // 计算菜单的尺寸
  const menuWidth = 160; // 菜单宽度（像素）
  const menuHeight = 100; // 菜单高度估计值（像素）

  // 计算安全的位置，确保不超出屏幕
  let safeX = touchX - menuWidth / 2;
  let safeY = touchY + 20; // 默认将菜单定位在触摸点下方

  // 确保菜单不超出屏幕左边界
  safeX = Math.max(10, safeX);
  // 确保菜单不超出屏幕右边界
  safeX = Math.min(screenWidth - menuWidth - 10, safeX);

  // 如果触摸点靠近屏幕底部，将菜单放在触摸点上方
  if (touchY > screenHeight - menuHeight - 60) {
    safeY = touchY - menuHeight - 20;
  }

  // 确保菜单不超出屏幕底部
  if (safeY + menuHeight > screenHeight - 10) {
    safeY = screenHeight - menuHeight - 10;
  }

  // 设置菜单位置和当前会话
  contextMenuPosition.value = { x: safeX, y: safeY };
  contextMenuSession.value = session;

  // 显示菜单
  nextTick(() => {
    showContextMenu.value = true;
  });
};

// 关闭上下文菜单
const closeContextMenu = () => {
  showContextMenu.value = false;
};

// 处理编辑完成
const handleEditComplete = async () => {
  if (!sessionToEdit.value) return;

  // 保存原始名称，以便失败时恢复
  const originalName = sessionToEdit.value.name;
  // 保存会话引用，防止在异步操作中丢失
  const sessionRef = sessionToEdit.value;

  try {

    // 调用重命名方法
    await historyStore.renameSession(sessionRef.sessionId, editNameInput.value);

    // 成功后显示提示并关闭弹窗
    toast('重命名成功');
    editPopupRef.value.close();

    // 成功后才清空编辑状态
    sessionToEdit.value = null;
  } catch (error) {
    // 隐藏加载指示器
    uni.hideLoading();

    console.error('重命名失败:', error);
    toast(error.message || '服务器繁忙，请稍后重试');

    // 如果会话对象还存在，恢复原始名称
    const sessionIndex = sessions.value.findIndex(s => s.sessionId === sessionRef.sessionId);
    if (sessionIndex !== -1) {
      sessions.value[sessionIndex].name = originalName;
    }
  }
};

// 关闭编辑弹窗
const closeEditPopup = () => {
  // 先取消聚焦
  inputFocus.value = false;
  editPopupRef.value.close();
  // 重置编辑状态
  sessionToEdit.value = null;
  // 清空输入框
  editNameInput.value = '';
};

// 监听弹窗状态变化
const handleEditPopupChange = (e) => {
  // 当弹窗关闭时
  if (!e.show) {
    inputFocus.value = false;
  }else{
    // 设置下一帧聚焦
    setTimeout(() => {
      inputFocus.value = true;
    },200);
  }
  
};

const historyStore = useHistoryStore();
const { sessions } = storeToRefs(historyStore);
const isLoading = ref(false);
const limit = ref(20);

// 监听 sessions 变化并自动加载
watch(() => sessions.value, (newSessions) => {
  console.log('会话列表已更新:', newSessions);
  if (newSessions && newSessions.length === 0) {
    // 如果会话列表为空，尝试加载
    loadHistory();
  }
}, { deep: true });

// 监听父组件中弹出层的关闭事件
const handlePopupChange = (e) => {
  // 当弹出层关闭时处理
  if (!e.show) {
    // 关闭上下文菜单
    showContextMenu.value = false;
  }
};

// 将这个方法暴露给父组件
defineExpose({
  handlePopupChange,
  handleEditPopupChange
});

// 添加初始化加载
onMounted(() => {
  // 获取胶囊按钮信息
  getCapsuleInfo();

  if ((sessions?.value?.length ?? 0) === 0) {
    loadHistory();
  }
});

// 加载历史记录
const loadHistory = async (isLoadMore = false) => {
  if (isLoading.value) return;

  isLoading.value = true;
  try {
    // 获取最后一条记录的ID（如果有）
    const lastId = sessions.value?.length > 0
      ? sessions.value[sessions.value.length - 1].id || sessions.value[sessions.value.length - 1].sessionId
      : null;

    console.log(`加载历史记录，isLoadMore=${isLoadMore}, lastId=${lastId}`);

    // 调用 store 的加载方法，传入分页参数
    await historyStore.loadConversations(
      limit.value,
      isLoadMore ? lastId : null // 只有滚动加载时才传递lastId
    );
  } catch (error) {
    console.error('读取历史记录失败:', error);
    toast('加载失败，请稍后重试');
  } finally {
    isLoading.value = false;
  }
};

// 添加滚动监听处理函数
const handleScrollToLower = () => {
  if (!isLoading.value && historyStore.hasMore) {
    console.log('触发加载更多');
    loadHistory(true);
  }
};

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`;
};

// 注释掉未使用的函数
// 解析Markdown表格并转换为ECharts数据的功能已移除

// 点击会话
const handleSessionClick = async (session) => {
  // 如果上下文菜单正在显示，关闭它并返回
  if (showContextMenu.value) {
    showContextMenu.value = false;
    return;
  }

  const chatStore = useChatStore()
  const { isLoading: isChatLoading } = storeToRefs(chatStore)

  if (isChatLoading.value) {
    toast('对话进行中，请稍后…')
    return
  }

  // 直接发送事件通知父组件，让父组件处理加载会话详情
  emit('select-session', session)
  
  // 发送重置用户滚动标识的事件
  emit('reset-scroll')
  
  // 关闭侧边栏
  handleClose()
}

// 用户store
const userStore = useUserStore()
const isLoggedIn = computed(() => userStore.checkIsLoggedIn())

</script>

<template>
  <!-- 编辑名称弹窗 -->
  <uni-popup ref="editPopupRef" type="center" :mask-click="false" @change="handleEditPopupChange">
    <view class="bg-white rd-lg p-4 w-80vw">
      <view class="text-lg font-500 mb-4">编辑名称</view>
      <input
        class="h-100rpx text-32 leading-100rpx pl-4 pr-8 border border-#D8D8D8 border-solid rounded-lg text-30 placeholder:text-30"
        v-model="editNameInput" :cursor="editNameInput.length" cursor-spacing='110' :adjust-position="true" placeholder="请输入会话名称" :focus="inputFocus" />
      <view class="flex justify-between items-center space-x-2 mt-4">
        <view>
          <wd-button @click="closeEditPopup" type="info"
            class="w-full !text-32rpx !h-100rpx flex items-center justify-center !rd-lg">
            取消
          </wd-button>
        </view>
        <view>
          <wd-button @click="handleEditComplete"
            class="w-full !text-32rpx !h-100rpx flex items-center justify-center !rd-lg">
            完成
          </wd-button>
        </view>
      </view>
    </view>
  </uni-popup>

  <!-- 长按菜单 -->
  <view v-if="showContextMenu" class="fixed z-50 pointer-events-auto  rd-lg menu-animation"
    :style="`top: ${contextMenuPosition.y}px; left: ${contextMenuPosition.x}px;`"
    style="box-shadow: 0 0 10px rgba(0, 0, 0, 0.15);">
    <view class="bg-white rd-lg overflow-hidden shadow-lg w-160px border border-gray-100">
      <view class="flex flex-col">
        <!-- 重命名选项 -->
        <view @click="handleEditClick(contextMenuSession)"
          class="flex items-center p-3 hover:bg-gray-100 active:bg-gray-200">
          <view class="i-material-symbols-edit-square-outline text-32 mr-3 text-gray-600"></view>
          <text class="text-gray-800 text-28">重命名</text>
        </view>

        <!-- 分割线 -->
        <view class="border-b border-b-gray-100 border-b-solid"></view>

        <!-- 删除选项 -->
        <view @click="handleDeleteClick(contextMenuSession)"
          class="flex items-center p-3 hover:bg-gray-100 active:bg-gray-200">
          <view class="i-material-symbols-delete-outline text-36 mr-3 text-red-500"></view>
          <text class="text-red-500 text-28">删除</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 遮罩层，点击关闭菜单 -->
  <view v-if="showContextMenu" class="fixed inset-0 z-10000" @click="closeContextMenu" @touchmove.stop.prevent></view>

  <view class="w-screen h-screen flex relative z-10000">
    <view class="flex flex-col h-screen w-85vw bg-white overflow-hidden box-border"
      :style="{ paddingTop: capsuleInfo.top + 'px' }">
      <!-- 标题区域 - 调整高度与胶囊按钮一致 -->
      <view class="flex items-center px-4" :style="{ height: capsuleInfo.height + 'px' }">
        <text class="text-36 font-600 text-#232323 leading-36rpx">会话历史</text>
      </view>

      <view v-if="(sessions?.length ?? 0) === 0" class="flex flex-col justify-center items-center text-gray-400"
        :style="{ height: `calc(100% - ${capsuleInfo.height}px - 80px)` }">
        <view class="i-ic-outline-history text-100 text-gray-300 mb-4"></view>
        <text class="text-28">暂无历史记录</text>
        <text class="text-24 text-gray-400 mt-2">创建新会话开始聊天吧</text>
      </view>

      <scroll-view v-else class="flex-1" :style="{ height: `calc(100% - ${capsuleInfo.height}px - 80px)` }" scroll-y
        @scrolltolower="handleScrollToLower" :show-scrollbar="false" :scroll-with-animation="true"
        :scroll-anchoring="true">
        <view class="px-2">
          <view v-for="session in sessions" :key="session.sessionId"
            class="flex flex-col p-3 py-4 relative box-border transition-all duration-300"
            :class="{ 'bg-gray-50': session.sessionId === historyStore.currentSession?.sessionId }"
            hover-class="bg-gray-50" @tap="handleSessionClick(session)" @longpress="handleLongPress(session, $event)">
            <!-- 会话内容部分 -->
            <view class="flex justify-between items-start">
              <!-- 左侧指示器（当前活跃项） -->
              <view v-if="session.sessionId === historyStore.currentSession?.sessionId"
                class="absolute left-0 top-0 bottom-0 w-1 bg-primary rd-r-md"></view>

              <!-- 标题和时间 -->
              <view class="w-full pr-2"
                :class="{ 'pl-2': session.sessionId === historyStore.currentSession?.sessionId }">
                <view class="flex justify-between items-center">
                  <view class="text-32 font-medium mb-10rpx truncate min-w-0 flex-1"
                    :class="{ 'text-primary': session.sessionId === historyStore.currentSession?.sessionId, 'text-gray-800': session.sessionId !== historyStore.currentSession?.sessionId }">
                    {{ session.name }}
                  </view>
                  <!-- 操作按钮已移除，改为长按菜单 -->
                </view>
                <view class="text-26 text-gray-500 flex items-center">
                  <text class="i-ic-outline-access-time mr-4rpx text-22"></text>
                  <text>{{ formatTime(session.timestamp) }}</text>
                </view>
              </view>

            </view>
          </view>
        </view>

        <!-- 加载状态 -->
        <view v-if="isLoading" class="p-4 text-center text-gray-400 flex flex-col justify-center items-center gap-2">
          <view class="w-5 h-5 border-2 border-solid border-gray-200 border-t-primary rounded-full animate-spin"></view>
          <text class="text-26">加载更多会话...</text>
        </view>

        <!-- 无更多数据提示 -->
        <view v-if="!isLoading && !historyStore.hasMore && sessions.length > 0"
          class="p-4 text-center text-gray-400 text-26">
          <text>没有更多会话了</text>
        </view>
      </scroll-view>

      <!-- 用户信息区域 -->
      <view class="box-border py-2 px-4 pb-40rpx" v-if="isLoggedIn"
        @click="() => { navigate('/pages/user/index'); handleClose(); }">
        <view class="rd-lg box-border flex items-center justify-between hover:bg-gray-100" hover-class="bg-gray-100">
          <view class="flex items-center">
            <!-- 用户头像 -->
            <view class="w-50 h-50 rd-full bg-primary flex items-center justify-center text-white text-32 mr-3">
              {{ userStore.userInfo?.username?.substring(0, 1)?.toUpperCase() || 'U' }}
            </view>

            <view class="space-y-1">
              <!-- 用户名 -->
              <view class="text-30 font-medium">{{ userStore.userInfo?.username || '用户' }}</view>
              <!-- 当前商户信息 -->
              <view v-if="userStore.currentMerchantObject" class="text-24 text-gray-500 flex items-center">
                <text class="i-ic-outline-store mr-1 text-32 h-30"></text>
                <text class="truncate max-w-40vw h-30 leading-28rpx">{{ userStore.currentMerchantObject.label || '未选择商户'
                  }}</text>
              </view>
            </view>
          </view>
          <!-- 右侧箭头 -->
          <view class="text-gray-300 flex items-center flex-shrink-0 pl-2">
            <view class="i-ic-round-arrow-forward-ios text-30"></view>
          </view>
        </view>
      </view>
    </view>
    <!-- Add a mask to cover the remaining 15vw space -->
    <view class="w-15vw h-screen bg-black bg-transparent" @click="handleClose"></view>
  </view>
</template>

<style>
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

/* 菜单动画 */
.menu-animation {
  animation: menu-fade-in 0.15s ease-out;
}

@keyframes menu-fade-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 增加滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
  background-color: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(200, 200, 200, 0.5);
  border-radius: 4px;
}
</style>